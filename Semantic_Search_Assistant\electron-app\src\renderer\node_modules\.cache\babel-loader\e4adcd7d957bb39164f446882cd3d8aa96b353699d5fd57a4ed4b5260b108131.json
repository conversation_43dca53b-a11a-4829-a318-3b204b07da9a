{"ast": null, "code": "import { animateMotionValue } from './motion-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction animateSingleValue(value, keyframes, options) {\n  const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n  motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n  return motionValue$1.animation;\n}\nexport { animateSingleValue };", "map": {"version": 3, "names": ["animateMotionValue", "motionValue", "isMotionValue", "animateSingleValue", "value", "keyframes", "options", "motionValue$1", "start", "animation"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/animation/interfaces/single-value.mjs"], "sourcesContent": ["import { animateMotionValue } from './motion-value.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACnD,MAAMC,aAAa,GAAGL,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAGH,WAAW,CAACG,KAAK,CAAC;EACvEG,aAAa,CAACC,KAAK,CAACR,kBAAkB,CAAC,EAAE,EAAEO,aAAa,EAAEF,SAAS,EAAEC,OAAO,CAAC,CAAC;EAC9E,OAAOC,aAAa,CAACE,SAAS;AAClC;AAEA,SAASN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}