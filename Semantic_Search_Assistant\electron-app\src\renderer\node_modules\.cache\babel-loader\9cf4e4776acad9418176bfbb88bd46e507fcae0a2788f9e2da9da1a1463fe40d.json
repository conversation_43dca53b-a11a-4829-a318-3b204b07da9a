{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Pointer = createLucideIcon(\"Pointer\", [[\"path\", {\n  d: \"M22 14a8 8 0 0 1-8 8\",\n  key: \"56vcr3\"\n}], [\"path\", {\n  d: \"M18 11v-1a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0\",\n  key: \"1pp0yd\"\n}], [\"path\", {\n  d: \"M14 10V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1\",\n  key: \"u654g\"\n}], [\"path\", {\n  d: \"M10 9.5V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v10\",\n  key: \"1e2dtv\"\n}], [\"path\", {\n  d: \"M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15\",\n  key: \"g6ys72\"\n}]]);\nexport { Pointer as default };", "map": {"version": 3, "names": ["Pointer", "createLucideIcon", "d", "key"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\pointer.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pointer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTRhOCA4IDAgMCAxLTggOCIgLz4KICA8cGF0aCBkPSJNMTggMTF2LTFhMiAyIDAgMCAwLTItMnYwYTIgMiAwIDAgMC0yIDJ2MCIgLz4KICA8cGF0aCBkPSJNMTQgMTBWOWEyIDIgMCAwIDAtMi0ydjBhMiAyIDAgMCAwLTIgMnYxIiAvPgogIDxwYXRoIGQ9Ik0xMCA5LjVWNGEyIDIgMCAwIDAtMi0ydjBhMiAyIDAgMCAwLTIgMnYxMCIgLz4KICA8cGF0aCBkPSJNMTggMTFhMiAyIDAgMSAxIDQgMHYzYTggOCAwIDAgMS04IDhoLTJjLTIuOCAwLTQuNS0uODYtNS45OS0yLjM0bC0zLjYtMy42YTIgMiAwIDAgMSAyLjgzLTIuODJMNyAxNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pointer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pointer = createLucideIcon('Pointer', [\n  ['path', { d: 'M22 14a8 8 0 0 1-8 8', key: '56vcr3' }],\n  ['path', { d: 'M18 11v-1a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0', key: '1pp0yd' }],\n  ['path', { d: 'M14 10V9a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v1', key: 'u654g' }],\n  ['path', { d: 'M10 9.5V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v10', key: '1e2dtv' }],\n  [\n    'path',\n    {\n      d: 'M18 11a2 2 0 1 1 4 0v3a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15',\n      key: 'g6ys72',\n    },\n  ],\n]);\n\nexport default Pointer;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAS,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}