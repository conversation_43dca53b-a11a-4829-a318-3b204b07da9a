[{"E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\index.js": "1", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\App.js": "2", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\FloatingApp.js": "3", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\FolderManager.js": "4", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\DocumentsView.js": "5", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\FolderFileBrowser.js": "6", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\ReadwiseImporter.js": "7", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\SearchInterface.js": "8", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\SettingsPanel.js": "9", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\Sidebar.js": "10", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\services\\ApiService.js": "11", "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\utils\\cn.js": "12"}, {"size": 674, "mtime": 1751818926003, "results": "13", "hashOfConfig": "14"}, {"size": 11584, "mtime": 1751884819391, "results": "15", "hashOfConfig": "14"}, {"size": 11178, "mtime": 1751819058833, "results": "16", "hashOfConfig": "14"}, {"size": 21172, "mtime": 1752516858869, "results": "17", "hashOfConfig": "14"}, {"size": 22975, "mtime": 1751914079470, "results": "18", "hashOfConfig": "14"}, {"size": 13890, "mtime": 1752516647660, "results": "19", "hashOfConfig": "14"}, {"size": 8456, "mtime": 1752515373463, "results": "20", "hashOfConfig": "14"}, {"size": 19106, "mtime": 1751884819439, "results": "21", "hashOfConfig": "14"}, {"size": 13828, "mtime": 1752515454800, "results": "22", "hashOfConfig": "14"}, {"size": 5241, "mtime": 1751827586081, "results": "23", "hashOfConfig": "14"}, {"size": 4482, "mtime": 1751884819523, "results": "24", "hashOfConfig": "14"}, {"size": 138, "mtime": 1751819118647, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rxz9nf", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\index.js", [], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\App.js", ["62", "63", "64", "65"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\FloatingApp.js", ["66", "67"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\FolderManager.js", ["68"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\DocumentsView.js", ["69", "70"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\FolderFileBrowser.js", ["71", "72"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\ReadwiseImporter.js", ["73"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\SearchInterface.js", ["74", "75", "76", "77", "78"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\SettingsPanel.js", ["79"], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\components\\Sidebar.js", [], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\services\\ApiService.js", [], [], "E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\src\\utils\\cn.js", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 40, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 40, "endColumn": 23}, {"ruleId": "80", "severity": 1, "message": "84", "line": 40, "column": 25, "nodeType": "82", "messageId": "83", "endLine": 40, "endColumn": 41}, {"ruleId": "85", "severity": 1, "message": "86", "line": 75, "column": 6, "nodeType": "87", "endLine": 75, "endColumn": 8, "suggestions": "88"}, {"ruleId": "89", "severity": 2, "message": "90", "line": 350, "column": 8, "nodeType": "91", "messageId": "92", "endLine": 350, "endColumn": 29}, {"ruleId": "80", "severity": 1, "message": "93", "line": 27, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 27, "endColumn": 20}, {"ruleId": "85", "severity": 1, "message": "94", "line": 72, "column": 6, "nodeType": "87", "endLine": 72, "endColumn": 8, "suggestions": "95"}, {"ruleId": null, "fatal": true, "severity": 2, "message": "96", "line": 540, "column": 6, "nodeType": null}, {"ruleId": "80", "severity": 1, "message": "97", "line": 25, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 25, "endColumn": 24}, {"ruleId": "85", "severity": 1, "message": "98", "line": 38, "column": 6, "nodeType": "87", "endLine": 38, "endColumn": 22, "suggestions": "99"}, {"ruleId": "80", "severity": 1, "message": "100", "line": 13, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 13, "endColumn": 6}, {"ruleId": "80", "severity": 1, "message": "101", "line": 14, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 14, "endColumn": 11}, {"ruleId": "80", "severity": 1, "message": "102", "line": 13, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 13, "endColumn": 12}, {"ruleId": "80", "severity": 1, "message": "103", "line": 32, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 32, "endColumn": 21}, {"ruleId": "80", "severity": 1, "message": "104", "line": 32, "column": 23, "nodeType": "82", "messageId": "83", "endLine": 32, "endColumn": 37}, {"ruleId": "80", "severity": 1, "message": "105", "line": 33, "column": 10, "nodeType": "82", "messageId": "83", "endLine": 33, "endColumn": 25}, {"ruleId": "80", "severity": 1, "message": "106", "line": 33, "column": 27, "nodeType": "82", "messageId": "83", "endLine": 33, "endColumn": 45}, {"ruleId": "80", "severity": 1, "message": "107", "line": 37, "column": 9, "nodeType": "82", "messageId": "83", "endLine": 37, "endColumn": 23}, {"ruleId": "85", "severity": 1, "message": "108", "line": 40, "column": 6, "nodeType": "87", "endLine": 40, "endColumn": 22, "suggestions": "109"}, "no-unused-vars", "'isCompactMode' is assigned a value but never used.", "Identifier", "unusedVar", "'setIsCompactMode' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initializeApp' and 'setupEventListeners'. Either include them or remove the dependency array.", "ArrayExpression", ["110"], "react/jsx-no-undef", "'ClipboardContextPanel' is not defined.", "JSXIdentifier", "undefined", "'isDragging' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeFloatingApp'. Either include it or remove the dependency array.", ["111"], "Parsing error: Unexpected token, expected \",\" (540:6)", "'indexingStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTestDocsFiles'. Either include it or remove the dependency array.", ["112"], "'Eye' is defined but never used.", "'Download' is defined but never used.", "'cn' is defined but never used.", "'suggestions' is assigned a value but never used.", "'setSuggestions' is assigned a value but never used.", "'showSuggestions' is assigned a value but never used.", "'setShowSuggestions' is assigned a value but never used.", "'dragPreviewRef' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSettings'. Either include it or remove the dependency array.", ["113"], {"desc": "114", "fix": "115"}, {"desc": "116", "fix": "117"}, {"desc": "118", "fix": "119"}, {"desc": "120", "fix": "121"}, "Update the dependencies array to be: [initializeApp, setupEventListeners]", {"range": "122", "text": "123"}, "Update the dependencies array to be: [initializeFloatingApp]", {"range": "124", "text": "125"}, "Update the dependencies array to be: [isBackendReady, loadTestDocsFiles]", {"range": "126", "text": "127"}, "Update the dependencies array to be: [isBackendReady, loadSettings]", {"range": "128", "text": "129"}, [2265, 2267], "[initializeApp, setupEventListeners]", [1670, 1672], "[initializeFloatingApp]", [1241, 1257], "[isBackendReady, loadTestDocsFiles]", [902, 918], "[isBackendReady, loadSettings]"]