{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UserRound = createLucideIcon(\"UserRound\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"1hypcn\"\n}], [\"path\", {\n  d: \"M20 21a8 8 0 0 0-16 0\",\n  key: \"rfgkzh\"\n}]]);\nexport { UserRound as default };", "map": {"version": 3, "names": ["UserRound", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\user-round.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjUiIC8+CiAgPHBhdGggZD0iTTIwIDIxYTggOCAwIDAgMC0xNiAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserRound = createLucideIcon('UserRound', [\n  ['circle', { cx: '12', cy: '8', r: '5', key: '1hypcn' }],\n  ['path', { d: 'M20 21a8 8 0 0 0-16 0', key: 'rfgkzh' }],\n]);\n\nexport default UserRound;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}