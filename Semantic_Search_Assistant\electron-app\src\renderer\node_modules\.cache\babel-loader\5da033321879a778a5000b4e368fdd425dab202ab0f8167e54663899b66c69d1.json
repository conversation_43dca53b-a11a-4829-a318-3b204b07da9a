{"ast": null, "code": "function renderHTML(element, _ref, styleProp, projection) {\n  let {\n    style,\n    vars\n  } = _ref;\n  Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n  // Loop over any CSS variables and assign those.\n  for (const key in vars) {\n    element.style.setProperty(key, vars[key]);\n  }\n}\nexport { renderHTML };", "map": {"version": 3, "names": ["renderHTML", "element", "_ref", "styleProp", "projection", "style", "vars", "Object", "assign", "getProjectionStyles", "key", "setProperty"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/render/html/utils/render.mjs"], "sourcesContent": ["function renderHTML(element, { style, vars }, styleProp, projection) {\n    Object.assign(element.style, style, projection && projection.getProjectionStyles(styleProp));\n    // Loop over any CSS variables and assign those.\n    for (const key in vars) {\n        element.style.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,OAAO,EAAAC,IAAA,EAAmBC,SAAS,EAAEC,UAAU,EAAE;EAAA,IAAxC;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAAJ,IAAA;EACxCK,MAAM,CAACC,MAAM,CAACP,OAAO,CAACI,KAAK,EAAEA,KAAK,EAAED,UAAU,IAAIA,UAAU,CAACK,mBAAmB,CAACN,SAAS,CAAC,CAAC;EAC5F;EACA,KAAK,MAAMO,GAAG,IAAIJ,IAAI,EAAE;IACpBL,OAAO,CAACI,KAAK,CAACM,WAAW,CAACD,GAAG,EAAEJ,IAAI,CAACI,GAAG,CAAC,CAAC;EAC7C;AACJ;AAEA,SAASV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}