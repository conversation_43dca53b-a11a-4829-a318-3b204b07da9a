{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ToggleRight = createLucideIcon(\"ToggleRight\", [[\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"6\",\n  ry: \"6\",\n  key: \"f2vt7d\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"4ma0v8\"\n}]]);\nexport { ToggleRight as default };", "map": {"version": 3, "names": ["ToggleRight", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "cx", "cy", "r"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\toggle-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ToggleRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjYiIHJ4PSI2IiByeT0iNiIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjEyIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/toggle-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleRight = createLucideIcon('ToggleRight', [\n  ['rect', { width: '20', height: '12', x: '2', y: '6', rx: '6', ry: '6', key: 'f2vt7d' }],\n  ['circle', { cx: '16', cy: '12', r: '2', key: '4ma0v8' }],\n]);\n\nexport default ToggleRight;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}