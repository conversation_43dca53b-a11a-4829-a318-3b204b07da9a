2025-07-14 23:05:39,715 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:07:26,496 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:08:26,769 - folder_manager - INFO - Loaded 1 connected folders
2025-07-14 23:08:26,776 - main - INFO - Initializing document search backend...
2025-07-14 23:08:26,776 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:08:26,783 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-14 23:08:26,783 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:08:35,832 - database - INFO - Connected to existing table: documents
2025-07-14 23:08:35,833 - database - INFO - Vector store initialized successfully
2025-07-14 23:08:35,835 - document_processor - INFO - Document processor initialized
2025-07-14 23:08:35,838 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:08:35,840 - folder_manager - INFO - Starting folder monitoring...
2025-07-14 23:08:35,845 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:08:35,849 - folder_manager - INFO - Monitoring 1 folders
2025-07-14 23:08:35,850 - main - INFO - Backend initialization complete
2025-07-14 23:08:35,851 - api_service - INFO - API server started successfully
2025-07-14 23:08:35,851 - folder_manager - INFO - Starting background document processor...
2025-07-14 23:17:55,549 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:18:06,954 - folder_manager - INFO - Loaded 1 connected folders
2025-07-14 23:18:06,954 - main - INFO - Initializing document search backend...
2025-07-14 23:18:06,954 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:18:06,955 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-14 23:18:06,955 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:18:13,221 - database - INFO - Connected to existing table: documents
2025-07-14 23:18:13,221 - database - INFO - Vector store initialized successfully
2025-07-14 23:18:13,221 - document_processor - INFO - Document processor initialized
2025-07-14 23:18:13,222 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:18:13,222 - folder_manager - INFO - Starting folder monitoring...
2025-07-14 23:18:13,226 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:18:13,226 - folder_manager - INFO - Monitoring 1 folders
2025-07-14 23:18:13,226 - main - INFO - Backend initialization complete
2025-07-14 23:18:13,226 - api_service - INFO - API server started successfully
2025-07-14 23:18:13,227 - folder_manager - INFO - Starting background document processor...
2025-07-14 23:18:13,236 - database - INFO - Vector store closed
2025-07-14 23:18:13,236 - api_service - INFO - API server shutdown complete
2025-07-14 23:18:13,237 - folder_manager - INFO - Background processor stopped
2025-07-14 23:18:22,792 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:18:32,877 - folder_manager - INFO - Loaded 1 connected folders
2025-07-14 23:18:32,877 - main - INFO - Initializing document search backend...
2025-07-14 23:18:32,877 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:18:32,878 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-14 23:18:32,879 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:18:39,561 - database - INFO - Connected to existing table: documents
2025-07-14 23:18:39,561 - database - INFO - Vector store initialized successfully
2025-07-14 23:18:39,562 - document_processor - INFO - Document processor initialized
2025-07-14 23:18:39,563 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:18:39,563 - folder_manager - INFO - Starting folder monitoring...
2025-07-14 23:18:39,566 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:18:39,567 - folder_manager - INFO - Monitoring 1 folders
2025-07-14 23:18:39,568 - main - INFO - Backend initialization complete
2025-07-14 23:18:39,569 - api_service - INFO - API server started successfully
2025-07-14 23:18:39,569 - folder_manager - INFO - Starting background document processor...
2025-07-14 23:18:39,573 - database - INFO - Vector store closed
2025-07-14 23:18:39,573 - api_service - INFO - API server shutdown complete
2025-07-14 23:18:39,576 - folder_manager - INFO - Background processor stopped
2025-07-14 23:19:18,962 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:19:28,512 - folder_manager - INFO - Loaded 1 connected folders
2025-07-14 23:19:28,512 - main - INFO - Initializing document search backend...
2025-07-14 23:19:28,513 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:19:28,513 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-14 23:19:28,514 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:19:34,087 - database - INFO - Connected to existing table: documents
2025-07-14 23:19:34,087 - database - INFO - Vector store initialized successfully
2025-07-14 23:19:34,088 - document_processor - INFO - Document processor initialized
2025-07-14 23:19:34,089 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:19:34,090 - folder_manager - INFO - Starting folder monitoring...
2025-07-14 23:19:34,093 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:19:34,093 - folder_manager - INFO - Monitoring 1 folders
2025-07-14 23:19:34,094 - main - INFO - Backend initialization complete
2025-07-14 23:19:34,095 - api_service - INFO - API server started successfully
2025-07-14 23:19:34,096 - folder_manager - INFO - Starting background document processor...
2025-07-14 23:19:34,102 - database - INFO - Vector store closed
2025-07-14 23:19:34,103 - api_service - INFO - API server shutdown complete
2025-07-14 23:19:34,105 - folder_manager - INFO - Background processor stopped
2025-07-14 23:23:18,518 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-14 23:23:36,231 - folder_manager - INFO - Loaded 1 connected folders
2025-07-14 23:23:36,231 - main - INFO - Initializing document search backend...
2025-07-14 23:23:36,231 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:23:36,232 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-14 23:23:36,233 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-14 23:23:41,894 - database - INFO - Connected to existing table: documents
2025-07-14 23:23:41,895 - database - INFO - Vector store initialized successfully
2025-07-14 23:23:41,895 - document_processor - INFO - Document processor initialized
2025-07-14 23:23:41,895 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:23:41,895 - folder_manager - INFO - Starting folder monitoring...
2025-07-14 23:23:41,896 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-14 23:23:41,897 - folder_manager - INFO - Monitoring 1 folders
2025-07-14 23:23:41,897 - main - INFO - Backend initialization complete
2025-07-14 23:23:41,897 - api_service - INFO - API server started successfully
2025-07-14 23:23:41,897 - folder_manager - INFO - Starting background document processor...
2025-07-14 23:24:55,023 - database - INFO - Vector store closed
2025-07-14 23:24:55,023 - api_service - INFO - API server shutdown complete
2025-07-14 23:24:55,024 - __main__ - INFO - Received signal 2, shutting down...
2025-07-14 23:24:55,025 - folder_manager - INFO - Background processor stopped
