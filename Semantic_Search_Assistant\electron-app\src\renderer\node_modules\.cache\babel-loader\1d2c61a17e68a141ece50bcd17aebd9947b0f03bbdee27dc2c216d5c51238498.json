{"ast": null, "code": "const isSVGTag = tag => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\nexport { isSVGTag };", "map": {"version": 3, "names": ["isSVGTag", "tag", "toLowerCase"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs"], "sourcesContent": ["const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;AAEhF,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}