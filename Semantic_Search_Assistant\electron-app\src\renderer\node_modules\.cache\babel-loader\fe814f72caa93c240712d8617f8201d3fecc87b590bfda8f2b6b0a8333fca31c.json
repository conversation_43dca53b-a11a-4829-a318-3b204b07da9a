{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Swords = createLucideIcon(\"Swords\", [[\"polyline\", {\n  points: \"14.5 17.5 3 6 3 3 6 3 17.5 14.5\",\n  key: \"1hfsw2\"\n}], [\"line\", {\n  x1: \"13\",\n  x2: \"19\",\n  y1: \"19\",\n  y2: \"13\",\n  key: \"1vrmhu\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"20\",\n  y1: \"16\",\n  y2: \"20\",\n  key: \"1bron3\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"21\",\n  y1: \"21\",\n  y2: \"19\",\n  key: \"13pww6\"\n}], [\"polyline\", {\n  points: \"14.5 6.5 18 3 21 3 21 6 17.5 9.5\",\n  key: \"hbey2j\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"9\",\n  y1: \"14\",\n  y2: \"18\",\n  key: \"1hf58s\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"4\",\n  y1: \"17\",\n  y2: \"20\",\n  key: \"pidxm4\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"5\",\n  y1: \"19\",\n  y2: \"21\",\n  key: \"1pehsh\"\n}]]);\nexport { Swords as default };", "map": {"version": 3, "names": ["Swords", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\swords.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Swords\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNC41IDE3LjUgMyA2IDMgMyA2IDMgMTcuNSAxNC41IiAvPgogIDxsaW5lIHgxPSIxMyIgeDI9IjE5IiB5MT0iMTkiIHkyPSIxMyIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyMCIgeTE9IjE2IiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjE5IiB4Mj0iMjEiIHkxPSIyMSIgeTI9IjE5IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjE0LjUgNi41IDE4IDMgMjEgMyAyMSA2IDE3LjUgOS41IiAvPgogIDxsaW5lIHgxPSI1IiB4Mj0iOSIgeTE9IjE0IiB5Mj0iMTgiIC8+CiAgPGxpbmUgeDE9IjciIHgyPSI0IiB5MT0iMTciIHkyPSIyMCIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjUiIHkxPSIxOSIgeTI9IjIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/swords\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Swords = createLucideIcon('Swords', [\n  ['polyline', { points: '14.5 17.5 3 6 3 3 6 3 17.5 14.5', key: '1hfsw2' }],\n  ['line', { x1: '13', x2: '19', y1: '19', y2: '13', key: '1vrmhu' }],\n  ['line', { x1: '16', x2: '20', y1: '16', y2: '20', key: '1bron3' }],\n  ['line', { x1: '19', x2: '21', y1: '21', y2: '19', key: '13pww6' }],\n  ['polyline', { points: '14.5 6.5 18 3 21 3 21 6 17.5 9.5', key: 'hbey2j' }],\n  ['line', { x1: '5', x2: '9', y1: '14', y2: '18', key: '1hf58s' }],\n  ['line', { x1: '7', x2: '4', y1: '17', y2: '20', key: 'pidxm4' }],\n  ['line', { x1: '3', x2: '5', y1: '19', y2: '21', key: '1pehsh' }],\n]);\n\nexport default Swords;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,UAAY;EAAEC,MAAA,EAAQ,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,UAAY;EAAED,MAAA,EAAQ,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}