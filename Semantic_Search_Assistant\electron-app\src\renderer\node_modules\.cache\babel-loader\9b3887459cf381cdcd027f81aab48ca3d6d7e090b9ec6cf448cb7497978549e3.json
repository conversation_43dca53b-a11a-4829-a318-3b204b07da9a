{"ast": null, "code": "import { isString, singleColorRegex, floatRegex } from '../utils.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => v => {\n  return Boolean(isString(v) && singleColorRegex.test(v) && v.startsWith(type) || testProp && Object.prototype.hasOwnProperty.call(v, testProp));\n};\nconst splitColor = (aName, bName, cName) => v => {\n  if (!isString(v)) return v;\n  const [a, b, c, alpha] = v.match(floatRegex);\n  return {\n    [aName]: parseFloat(a),\n    [bName]: parseFloat(b),\n    [cName]: parseFloat(c),\n    alpha: alpha !== undefined ? parseFloat(alpha) : 1\n  };\n};\nexport { isColorString, splitColor };", "map": {"version": 3, "names": ["isString", "singleColorRegex", "floatRegex", "isColorString", "type", "testProp", "v", "Boolean", "test", "startsWith", "Object", "prototype", "hasOwnProperty", "call", "splitColor", "aName", "bName", "cName", "a", "b", "c", "alpha", "match", "parseFloat", "undefined"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/value/types/color/utils.mjs"], "sourcesContent": ["import { isString, singleColorRegex, floatRegex } from '../utils.mjs';\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((isString(v) && singleColorRegex.test(v) && v.startsWith(type)) ||\n        (testProp && Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (!isString(v))\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,cAAc;;AAErE;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAMC,CAAC,IAAK;EAC7C,OAAOC,OAAO,CAAEP,QAAQ,CAACM,CAAC,CAAC,IAAIL,gBAAgB,CAACO,IAAI,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACG,UAAU,CAACL,IAAI,CAAC,IACxEC,QAAQ,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAED,QAAQ,CAAE,CAAC;AACxE,CAAC;AACD,MAAMS,UAAU,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAMX,CAAC,IAAK;EAC/C,IAAI,CAACN,QAAQ,CAACM,CAAC,CAAC,EACZ,OAAOA,CAAC;EACZ,MAAM,CAACY,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,GAAGf,CAAC,CAACgB,KAAK,CAACpB,UAAU,CAAC;EAC5C,OAAO;IACH,CAACa,KAAK,GAAGQ,UAAU,CAACL,CAAC,CAAC;IACtB,CAACF,KAAK,GAAGO,UAAU,CAACJ,CAAC,CAAC;IACtB,CAACF,KAAK,GAAGM,UAAU,CAACH,CAAC,CAAC;IACtBC,KAAK,EAAEA,KAAK,KAAKG,SAAS,GAAGD,UAAU,CAACF,KAAK,CAAC,GAAG;EACrD,CAAC;AACL,CAAC;AAED,SAASlB,aAAa,EAAEW,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}