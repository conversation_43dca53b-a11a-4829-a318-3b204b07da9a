{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PlugZap2 = createLucideIcon(\"PlugZap2\", [[\"path\", {\n  d: \"m13 2-2 2.5h3L12 7\",\n  key: \"1me98u\"\n}], [\"path\", {\n  d: \"M10 14v-3\",\n  key: \"1mllf3\"\n}], [\"path\", {\n  d: \"M14 14v-3\",\n  key: \"1l3fkq\"\n}], [\"path\", {\n  d: \"M11 19c-1.7 0-3-1.3-3-3v-2h8v2c0 1.7-1.3 3-3 3Z\",\n  key: \"jd5pat\"\n}], [\"path\", {\n  d: \"M12 22v-3\",\n  key: \"kmzjlo\"\n}]]);\nexport { PlugZap2 as default };", "map": {"version": 3, "names": ["PlugZap2", "createLucideIcon", "d", "key"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\plug-zap-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PlugZap2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTMgMi0yIDIuNWgzTDEyIDciIC8+CiAgPHBhdGggZD0iTTEwIDE0di0zIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNHYtMyIgLz4KICA8cGF0aCBkPSJNMTEgMTljLTEuNyAwLTMtMS4zLTMtM3YtMmg4djJjMCAxLjctMS4zIDMtMyAzWiIgLz4KICA8cGF0aCBkPSJNMTIgMjJ2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/plug-zap-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PlugZap2 = createLucideIcon('PlugZap2', [\n  ['path', { d: 'm13 2-2 2.5h3L12 7', key: '1me98u' }],\n  ['path', { d: 'M10 14v-3', key: '1mllf3' }],\n  ['path', { d: 'M14 14v-3', key: '1l3fkq' }],\n  ['path', { d: 'M11 19c-1.7 0-3-1.3-3-3v-2h8v2c0 1.7-1.3 3-3 3Z', key: 'jd5pat' }],\n  ['path', { d: 'M12 22v-3', key: 'kmzjlo' }],\n]);\n\nexport default PlugZap2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}