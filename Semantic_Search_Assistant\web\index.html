<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Semantic Search Assistant</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .search-box {
        width: 100%;
        padding: 15px;
        font-size: 16px;
        border: 2px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .search-box:focus {
        outline: none;
        border-color: #007bff;
      }
      .btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
      }
      .btn:hover {
        background: #0056b3;
      }
      .results {
        margin-top: 20px;
      }
      .result-item {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        background: #f9f9f9;
      }
      .status {
        text-align: center;
        padding: 20px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Semantic Search Assistant</h1>
        <p>Search your documents using natural language</p>
      </div>

      <div>
        <input
          type="text"
          id="searchInput"
          class="search-box"
          placeholder="Enter your search query..."
        />
        <button onclick="performSearch()" class="btn">Search</button>
      </div>

      <div id="results" class="results"></div>
      <div id="status" class="status">Ready to search</div>
    </div>

    <script>
      async function performSearch() {
        const query = document.getElementById("searchInput").value;
        const resultsDiv = document.getElementById("results");
        const statusDiv = document.getElementById("status");

        if (!query.trim()) {
          statusDiv.textContent = "Please enter a search query";
          return;
        }

        statusDiv.textContent = "Searching...";
        resultsDiv.innerHTML = "";

        try {
          const response = await fetch("http://127.0.0.1:8001/search", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              query: query,
              limit: 20,
              similarity_threshold: 0.3,
            }),
          });

          const data = await response.json();

          if (data.results && data.results.length > 0) {
            statusDiv.textContent = `Found ${data.results.length} results`;

            data.results.forEach((result) => {
              const resultDiv = document.createElement("div");
              resultDiv.className = "result-item";
              resultDiv.innerHTML = `
                            <h3>${result.source || "Unknown Source"}</h3>
                            <p>${result.content || "No content available"}</p>
                            <small>Similarity: ${(
                              result.similarity * 100
                            ).toFixed(1)}%</small>
                        `;
              resultsDiv.appendChild(resultDiv);
            });
          } else {
            statusDiv.textContent = "No results found";
          }
        } catch (error) {
          statusDiv.textContent = "Error: " + error.message;
          console.error("Search error:", error);
        }
      }

      // Allow Enter key to trigger search
      document
        .getElementById("searchInput")
        .addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            performSearch();
          }
        });

      // Check backend status on load
      async function checkBackend() {
        try {
          const response = await fetch("http://127.0.0.1:8001/health");
          const data = await response.json();
          document.getElementById("status").textContent =
            "Backend connected and ready";
        } catch (error) {
          document.getElementById("status").textContent =
            "Backend not available - please start the backend first";
        }
      }

      checkBackend();
    </script>
  </body>
</html>
