{"ast": null, "code": "/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = v => /^\\-?\\d*\\.?\\d+$/.test(v);\nexport { isNumericalString };", "map": {"version": 3, "names": ["isNumericalString", "v", "test"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/utils/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^\\-?\\d*\\.?\\d+$/.test(v);\n\nexport { isNumericalString };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,CAAC,IAAK,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAEzD,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}