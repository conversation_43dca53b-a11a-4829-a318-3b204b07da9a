{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SprayCan = createLucideIcon(\"SprayCan\", [[\"path\", {\n  d: \"M3 3h.01\",\n  key: \"159qn6\"\n}], [\"path\", {\n  d: \"M7 5h.01\",\n  key: \"1hq22a\"\n}], [\"path\", {\n  d: \"M11 7h.01\",\n  key: \"1osv80\"\n}], [\"path\", {\n  d: \"M3 7h.01\",\n  key: \"1xzrh3\"\n}], [\"path\", {\n  d: \"M7 9h.01\",\n  key: \"19b3jx\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"4\",\n  x: \"15\",\n  y: \"5\",\n  key: \"mri9e4\"\n}], [\"path\", {\n  d: \"m19 9 2 2v10c0 .6-.4 1-1 1h-6c-.6 0-1-.4-1-1V11l2-2\",\n  key: \"aib6hk\"\n}], [\"path\", {\n  d: \"m13 14 8-2\",\n  key: \"1d7bmk\"\n}], [\"path\", {\n  d: \"m13 19 8-2\",\n  key: \"1y2vml\"\n}]]);\nexport { SprayCan as default };", "map": {"version": 3, "names": ["SprayCan", "createLucideIcon", "d", "key", "width", "height", "x", "y"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\spray-can.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SprayCan\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzaC4wMSIgLz4KICA8cGF0aCBkPSJNNyA1aC4wMSIgLz4KICA8cGF0aCBkPSJNMTEgN2guMDEiIC8+CiAgPHBhdGggZD0iTTMgN2guMDEiIC8+CiAgPHBhdGggZD0iTTcgOWguMDEiIC8+CiAgPHBhdGggZD0iTTMgMTFoLjAxIiAvPgogIDxyZWN0IHdpZHRoPSI0IiBoZWlnaHQ9IjQiIHg9IjE1IiB5PSI1IiAvPgogIDxwYXRoIGQ9Im0xOSA5IDIgMnYxMGMwIC42LS40IDEtMSAxaC02Yy0uNiAwLTEtLjQtMS0xVjExbDItMiIgLz4KICA8cGF0aCBkPSJtMTMgMTQgOC0yIiAvPgogIDxwYXRoIGQ9Im0xMyAxOSA4LTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/spray-can\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SprayCan = createLucideIcon('SprayCan', [\n  ['path', { d: 'M3 3h.01', key: '159qn6' }],\n  ['path', { d: 'M7 5h.01', key: '1hq22a' }],\n  ['path', { d: 'M11 7h.01', key: '1osv80' }],\n  ['path', { d: 'M3 7h.01', key: '1xzrh3' }],\n  ['path', { d: 'M7 9h.01', key: '19b3jx' }],\n  ['path', { d: 'M3 11h.01', key: '1eifu7' }],\n  ['rect', { width: '4', height: '4', x: '15', y: '5', key: 'mri9e4' }],\n  ['path', { d: 'm19 9 2 2v10c0 .6-.4 1-1 1h-6c-.6 0-1-.4-1-1V11l2-2', key: 'aib6hk' }],\n  ['path', { d: 'm13 14 8-2', key: '1d7bmk' }],\n  ['path', { d: 'm13 19 8-2', key: '1y2vml' }],\n]);\n\nexport default SprayCan;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}