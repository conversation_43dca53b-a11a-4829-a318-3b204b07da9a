{"ast": null, "code": "import { mapEasingToNativeEasing } from './easing.mjs';\nfunction animateStyle(element, valueName, keyframes) {\n  let {\n    delay = 0,\n    duration,\n    repeat = 0,\n    repeatType = \"loop\",\n    ease,\n    times\n  } = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  const keyframeOptions = {\n    [valueName]: keyframes\n  };\n  if (times) keyframeOptions.offset = times;\n  const easing = mapEasingToNativeEasing(ease);\n  /**\n   * If this is an easing array, apply to keyframes, not animation as a whole\n   */\n  if (Array.isArray(easing)) keyframeOptions.easing = easing;\n  return element.animate(keyframeOptions, {\n    delay,\n    duration,\n    easing: !Array.isArray(easing) ? easing : \"linear\",\n    fill: \"both\",\n    iterations: repeat + 1,\n    direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\"\n  });\n}\nexport { animateStyle };", "map": {"version": 3, "names": ["mapEasingToNativeEasing", "animateStyle", "element", "valueName", "keyframes", "delay", "duration", "repeat", "repeatType", "ease", "times", "arguments", "length", "undefined", "keyframeOptions", "offset", "easing", "Array", "isArray", "animate", "fill", "iterations", "direction"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs"], "sourcesContent": ["import { mapEasingToNativeEasing } from './easing.mjs';\n\nfunction animateStyle(element, valueName, keyframes, { delay = 0, duration, repeat = 0, repeatType = \"loop\", ease, times, } = {}) {\n    const keyframeOptions = { [valueName]: keyframes };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = mapEasingToNativeEasing(ease);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    return element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    });\n}\n\nexport { animateStyle };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,cAAc;AAEtD,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAA+E;EAAA,IAA7E;IAAEC,KAAK,GAAG,CAAC;IAAEC,QAAQ;IAAEC,MAAM,GAAG,CAAC;IAAEC,UAAU,GAAG,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5H,MAAMG,eAAe,GAAG;IAAE,CAACX,SAAS,GAAGC;EAAU,CAAC;EAClD,IAAIM,KAAK,EACLI,eAAe,CAACC,MAAM,GAAGL,KAAK;EAClC,MAAMM,MAAM,GAAGhB,uBAAuB,CAACS,IAAI,CAAC;EAC5C;AACJ;AACA;EACI,IAAIQ,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EACrBF,eAAe,CAACE,MAAM,GAAGA,MAAM;EACnC,OAAOd,OAAO,CAACiB,OAAO,CAACL,eAAe,EAAE;IACpCT,KAAK;IACLC,QAAQ;IACRU,MAAM,EAAE,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,QAAQ;IAClDI,IAAI,EAAE,MAAM;IACZC,UAAU,EAAEd,MAAM,GAAG,CAAC;IACtBe,SAAS,EAAEd,UAAU,KAAK,SAAS,GAAG,WAAW,GAAG;EACxD,CAAC,CAAC;AACN;AAEA,SAASP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}