{"ast": null, "code": "/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = v => /^0[^.\\s]+$/.test(v);\nexport { isZeroValueString };", "map": {"version": 3, "names": ["isZeroValueString", "v", "test"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/utils/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/.test(v);\n\nexport { isZeroValueString };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,CAAC,IAAK,YAAY,CAACC,IAAI,CAACD,CAAC,CAAC;AAErD,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}