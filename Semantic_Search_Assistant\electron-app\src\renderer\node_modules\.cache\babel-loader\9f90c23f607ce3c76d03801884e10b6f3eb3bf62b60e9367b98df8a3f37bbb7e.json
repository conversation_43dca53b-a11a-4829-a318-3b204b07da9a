{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import axios from\"axios\";import{motion,AnimatePresence}from\"framer-motion\";import{Folder,File,FileText,FileImage,X,Search,Check,RefreshCw,Eye,Download}from\"lucide-react\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const FolderFileBrowser=_ref=>{let{isOpen,onClose,onFileSelect,showProcessOption=false,onProcessFiles}=_ref;const[connectedFolders,setConnectedFolders]=useState([]);const[selectedFolder,setSelectedFolder]=useState(null);const[folderFiles,setFolderFiles]=useState([]);const[selectedFiles,setSelectedFiles]=useState([]);const[isLoading,setIsLoading]=useState(false);const[searchQuery,setSearchQuery]=useState(\"\");const[fileFilter,setFileFilter]=useState(\"all\");useEffect(()=>{if(isOpen){loadConnectedFolders();}},[isOpen]);const loadConnectedFolders=async()=>{try{setIsLoading(true);const response=await axios.get(\"http://127.0.0.1:8000/folders/list\");setConnectedFolders(response.data.connected_folders||[]);}catch(error){console.error(\"Failed to load folders:\",error);}finally{setIsLoading(false);}};const loadFolderFiles=async folderPath=>{try{setIsLoading(true);setSelectedFolder(folderPath);// Get files from the folder by scanning it\nconst response=await axios.post(\"http://127.0.0.1:8000/folders/scan\",{folder_path:folderPath});setFolderFiles(response.data.files||[]);}catch(error){console.error(\"Failed to load folder files:\",error);// Fallback: try to get processed files info\ntry{var _statsResponse$data$s,_statsResponse$data$s2;const statsResponse=await axios.get(\"http://127.0.0.1:8000/folders/list\");const folderStats=(_statsResponse$data$s=statsResponse.data.stats)===null||_statsResponse$data$s===void 0?void 0:(_statsResponse$data$s2=_statsResponse$data$s.folders)===null||_statsResponse$data$s2===void 0?void 0:_statsResponse$data$s2.find(f=>f.path===folderPath);if(folderStats){// Create mock file list from stats (this is a fallback)\nsetFolderFiles([]);}}catch(fallbackError){console.error(\"Fallback failed:\",fallbackError);setFolderFiles([]);}}finally{setIsLoading(false);}};const getFileIcon=fileName=>{var _fileName$split$pop;const extension=(_fileName$split$pop=fileName.split(\".\").pop())===null||_fileName$split$pop===void 0?void 0:_fileName$split$pop.toLowerCase();switch(extension){case\"pdf\":return/*#__PURE__*/_jsx(FileText,{className:\"w-5 h-5 text-red-500\"});case\"docx\":case\"doc\":return/*#__PURE__*/_jsx(FileText,{className:\"w-5 h-5 text-blue-500\"});case\"md\":case\"txt\":return/*#__PURE__*/_jsx(FileText,{className:\"w-5 h-5 text-gray-500\"});case\"jpg\":case\"jpeg\":case\"png\":case\"gif\":return/*#__PURE__*/_jsx(FileImage,{className:\"w-5 h-5 text-green-500\"});default:return/*#__PURE__*/_jsx(File,{className:\"w-5 h-5 text-gray-400\"});}};const formatFileSize=bytes=>{if(bytes===0)return\"0 Bytes\";const k=1024;const sizes=[\"Bytes\",\"KB\",\"MB\",\"GB\"];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+\" \"+sizes[i];};const formatDate=timestamp=>{return new Date(timestamp*1000).toLocaleDateString();};const toggleFileSelection=file=>{setSelectedFiles(prev=>{const isSelected=prev.some(f=>f.path===file.path);if(isSelected){return prev.filter(f=>f.path!==file.path);}else{return[...prev,file];}});};const handleSelectFiles=()=>{if(selectedFiles.length>0){onFileSelect(selectedFiles);onClose();}};const handleProcessFiles=()=>{if(selectedFiles.length>0&&onProcessFiles){onProcessFiles(selectedFiles);onClose();}};const filteredFiles=folderFiles.filter(file=>{const matchesSearch=file.name.toLowerCase().includes(searchQuery.toLowerCase());const matchesFilter=fileFilter===\"all\"||file.extension===\".\".concat(fileFilter);return matchesSearch&&matchesFilter;});if(!isOpen)return null;return/*#__PURE__*/_jsx(AnimatePresence,{children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",onClick:onClose,children:/*#__PURE__*/_jsxs(motion.div,{initial:{scale:0.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0.9,opacity:0},className:\"bg-white rounded-lg shadow-xl w-4/5 h-4/5 max-w-6xl max-h-4xl flex flex-col\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-4 border-b\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Browse Folder Files\"}),/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"p-2 hover:bg-gray-100 rounded-full\",children:/*#__PURE__*/_jsx(X,{className:\"w-5 h-5\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-1 overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-1/3 border-r bg-gray-50 p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-medium mb-3\",children:\"Connected Folders\"}),isLoading&&!selectedFolder?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 text-gray-500\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-4 h-4 animate-spin\"}),\"Loading folders...\"]}):/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[connectedFolders.map((folder,index)=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>loadFolderFiles(folder),className:\"w-full text-left p-3 rounded-md hover:bg-white transition-colors \".concat(selectedFolder===folder?\"bg-blue-100 border border-blue-300\":\"bg-white\"),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(Folder,{className:\"w-4 h-4 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm truncate\",title:folder,children:folder.split(\"\\\\\").pop()||folder.split(\"/\").pop()})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500 mt-1 truncate\",children:folder})]},index)),connectedFolders.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-500 text-sm\",children:\"No folders connected. Add folders in the Folders tab.\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col\",children:selectedFolder?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-4 border-b bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-4 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 relative\",children:[/*#__PURE__*/_jsx(Search,{className:\"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search files...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),className:\"w-full pl-10 pr-4 py-2 border rounded-md\"})]}),/*#__PURE__*/_jsxs(\"select\",{value:fileFilter,onChange:e=>setFileFilter(e.target.value),className:\"px-3 py-2 border rounded-md\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Files\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pdf\",children:\"PDF\"}),/*#__PURE__*/_jsx(\"option\",{value:\"docx\",children:\"Word\"}),/*#__PURE__*/_jsx(\"option\",{value:\"md\",children:\"Markdown\"}),/*#__PURE__*/_jsx(\"option\",{value:\"txt\",children:\"Text\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 overflow-auto p-4\",children:isLoading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center h-32\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-6 h-6 animate-spin text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-gray-500\",children:\"Loading files...\"})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-2\",children:[filteredFiles.map((file,index)=>{const isSelected=selectedFiles.some(f=>f.path===file.path);return/*#__PURE__*/_jsx(\"div\",{onClick:()=>toggleFileSelection(file),className:\"p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors \".concat(isSelected?\"bg-blue-50 border-blue-300\":\"border-gray-200\"),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-shrink-0\",children:[isSelected&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute -mt-1 -ml-1 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(Check,{className:\"w-3 h-3 text-white\"})}),getFileIcon(file.name)]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm truncate\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-500 flex gap-4\",children:[/*#__PURE__*/_jsx(\"span\",{children:formatFileSize(file.size)}),/*#__PURE__*/_jsx(\"span\",{children:formatDate(file.modified)}),file.needs_processing&&/*#__PURE__*/_jsx(\"span\",{className:\"text-orange-600\",children:\"Needs Processing\"})]})]})]})},index);}),filteredFiles.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8 text-gray-500\",children:searchQuery||fileFilter!==\"all\"?\"No files match your search criteria\":\"No files found in this folder\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 border-t bg-gray-50 flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600\",children:[selectedFiles.length,\" file(s) selected\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:onClose,className:\"px-4 py-2 text-gray-600 hover:bg-gray-200 rounded-md\",children:\"Cancel\"}),showProcessOption&&onProcessFiles&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleProcessFiles,disabled:selectedFiles.length===0,className:\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\",children:[\"Process Files (\",selectedFiles.length,\")\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSelectFiles,disabled:selectedFiles.length===0,className:\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",children:[\"Select Files (\",selectedFiles.length,\")\"]})]})]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex items-center justify-center text-gray-500\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Folder,{className:\"w-12 h-12 mx-auto mb-3 text-gray-400\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Select a folder to browse its files\"})]})})})]})]})})});};export default FolderFileBrowser;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "motion", "AnimatePresence", "Folder", "File", "FileText", "FileImage", "X", "Search", "Check", "RefreshCw", "Eye", "Download", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "FolderFileBrowser", "_ref", "isOpen", "onClose", "onFileSelect", "showProcessOption", "onProcessFiles", "connectedFolders", "setConnectedFolders", "selectedFolder", "setSelectedFolder", "folderFiles", "setFolderFiles", "selectedFiles", "setSelectedFiles", "isLoading", "setIsLoading", "searchQuery", "setSearch<PERSON>uery", "fileFilter", "setFileFilter", "loadConnectedFolders", "response", "get", "data", "connected_folders", "error", "console", "loadFolderFiles", "folderPath", "post", "folder_path", "files", "_statsResponse$data$s", "_statsResponse$data$s2", "statsResponse", "folderStats", "stats", "folders", "find", "f", "path", "fallback<PERSON><PERSON>r", "getFileIcon", "fileName", "_fileName$split$pop", "extension", "split", "pop", "toLowerCase", "className", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatDate", "timestamp", "Date", "toLocaleDateString", "toggleFileSelection", "file", "prev", "isSelected", "some", "filter", "handleSelectFiles", "length", "handleProcessFiles", "filteredFiles", "matchesSearch", "name", "includes", "matchesFilter", "concat", "children", "div", "initial", "opacity", "animate", "exit", "onClick", "scale", "e", "stopPropagation", "map", "folder", "index", "title", "type", "placeholder", "value", "onChange", "target", "size", "modified", "needs_processing", "disabled"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/src/components/FolderFileBrowser.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Folder,\n  File,\n  FileText,\n  FileImage,\n  X,\n  Search,\n  Check,\n  RefreshCw,\n  Eye,\n  Download,\n} from \"lucide-react\";\n\nconst FolderFileBrowser = ({\n  isOpen,\n  onClose,\n  onFileSelect,\n  showProcessOption = false,\n  onProcessFiles,\n}) => {\n  const [connectedFolders, setConnectedFolders] = useState([]);\n  const [selectedFolder, setSelectedFolder] = useState(null);\n  const [folderFiles, setFolderFiles] = useState([]);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [fileFilter, setFileFilter] = useState(\"all\");\n\n  useEffect(() => {\n    if (isOpen) {\n      loadConnectedFolders();\n    }\n  }, [isOpen]);\n\n  const loadConnectedFolders = async () => {\n    try {\n      setIsLoading(true);\n      const response = await axios.get(\"http://127.0.0.1:8000/folders/list\");\n      setConnectedFolders(response.data.connected_folders || []);\n    } catch (error) {\n      console.error(\"Failed to load folders:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadFolderFiles = async (folderPath) => {\n    try {\n      setIsLoading(true);\n      setSelectedFolder(folderPath);\n\n      // Get files from the folder by scanning it\n      const response = await axios.post(\"http://127.0.0.1:8000/folders/scan\", {\n        folder_path: folderPath,\n      });\n\n      setFolderFiles(response.data.files || []);\n    } catch (error) {\n      console.error(\"Failed to load folder files:\", error);\n      // Fallback: try to get processed files info\n      try {\n        const statsResponse = await axios.get(\n          \"http://127.0.0.1:8000/folders/list\"\n        );\n        const folderStats = statsResponse.data.stats?.folders?.find(\n          (f) => f.path === folderPath\n        );\n        if (folderStats) {\n          // Create mock file list from stats (this is a fallback)\n          setFolderFiles([]);\n        }\n      } catch (fallbackError) {\n        console.error(\"Fallback failed:\", fallbackError);\n        setFolderFiles([]);\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getFileIcon = (fileName) => {\n    const extension = fileName.split(\".\").pop()?.toLowerCase();\n    switch (extension) {\n      case \"pdf\":\n        return <FileText className=\"w-5 h-5 text-red-500\" />;\n      case \"docx\":\n      case \"doc\":\n        return <FileText className=\"w-5 h-5 text-blue-500\" />;\n      case \"md\":\n      case \"txt\":\n        return <FileText className=\"w-5 h-5 text-gray-500\" />;\n      case \"jpg\":\n      case \"jpeg\":\n      case \"png\":\n      case \"gif\":\n        return <FileImage className=\"w-5 h-5 text-green-500\" />;\n      default:\n        return <File className=\"w-5 h-5 text-gray-400\" />;\n    }\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n  };\n\n  const formatDate = (timestamp) => {\n    return new Date(timestamp * 1000).toLocaleDateString();\n  };\n\n  const toggleFileSelection = (file) => {\n    setSelectedFiles((prev) => {\n      const isSelected = prev.some((f) => f.path === file.path);\n      if (isSelected) {\n        return prev.filter((f) => f.path !== file.path);\n      } else {\n        return [...prev, file];\n      }\n    });\n  };\n\n  const handleSelectFiles = () => {\n    if (selectedFiles.length > 0) {\n      onFileSelect(selectedFiles);\n      onClose();\n    }\n  };\n\n  const handleProcessFiles = () => {\n    if (selectedFiles.length > 0 && onProcessFiles) {\n      onProcessFiles(selectedFiles);\n      onClose();\n    }\n  };\n\n  const filteredFiles = folderFiles.filter((file) => {\n    const matchesSearch = file.name\n      .toLowerCase()\n      .includes(searchQuery.toLowerCase());\n    const matchesFilter =\n      fileFilter === \"all\" || file.extension === `.${fileFilter}`;\n    return matchesSearch && matchesFilter;\n  });\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          className=\"bg-white rounded-lg shadow-xl w-4/5 h-4/5 max-w-6xl max-h-4xl flex flex-col\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b\">\n            <h2 className=\"text-xl font-semibold\">Browse Folder Files</h2>\n            <button\n              onClick={onClose}\n              className=\"p-2 hover:bg-gray-100 rounded-full\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          <div className=\"flex flex-1 overflow-hidden\">\n            {/* Sidebar - Folders */}\n            <div className=\"w-1/3 border-r bg-gray-50 p-4\">\n              <h3 className=\"font-medium mb-3\">Connected Folders</h3>\n              {isLoading && !selectedFolder ? (\n                <div className=\"flex items-center gap-2 text-gray-500\">\n                  <RefreshCw className=\"w-4 h-4 animate-spin\" />\n                  Loading folders...\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  {connectedFolders.map((folder, index) => (\n                    <button\n                      key={index}\n                      onClick={() => loadFolderFiles(folder)}\n                      className={`w-full text-left p-3 rounded-md hover:bg-white transition-colors ${\n                        selectedFolder === folder\n                          ? \"bg-blue-100 border border-blue-300\"\n                          : \"bg-white\"\n                      }`}\n                    >\n                      <div className=\"flex items-center gap-2\">\n                        <Folder className=\"w-4 h-4 text-blue-600\" />\n                        <span className=\"text-sm truncate\" title={folder}>\n                          {folder.split(\"\\\\\").pop() || folder.split(\"/\").pop()}\n                        </span>\n                      </div>\n                      <div className=\"text-xs text-gray-500 mt-1 truncate\">\n                        {folder}\n                      </div>\n                    </button>\n                  ))}\n                  {connectedFolders.length === 0 && (\n                    <div className=\"text-gray-500 text-sm\">\n                      No folders connected. Add folders in the Folders tab.\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Main content - Files */}\n            <div className=\"flex-1 flex flex-col\">\n              {selectedFolder ? (\n                <>\n                  {/* Search and filters */}\n                  <div className=\"p-4 border-b bg-gray-50\">\n                    <div className=\"flex gap-4 items-center\">\n                      <div className=\"flex-1 relative\">\n                        <Search className=\"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                        <input\n                          type=\"text\"\n                          placeholder=\"Search files...\"\n                          value={searchQuery}\n                          onChange={(e) => setSearchQuery(e.target.value)}\n                          className=\"w-full pl-10 pr-4 py-2 border rounded-md\"\n                        />\n                      </div>\n                      <select\n                        value={fileFilter}\n                        onChange={(e) => setFileFilter(e.target.value)}\n                        className=\"px-3 py-2 border rounded-md\"\n                      >\n                        <option value=\"all\">All Files</option>\n                        <option value=\"pdf\">PDF</option>\n                        <option value=\"docx\">Word</option>\n                        <option value=\"md\">Markdown</option>\n                        <option value=\"txt\">Text</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  {/* File list */}\n                  <div className=\"flex-1 overflow-auto p-4\">\n                    {isLoading ? (\n                      <div className=\"flex items-center justify-center h-32\">\n                        <RefreshCw className=\"w-6 h-6 animate-spin text-gray-400\" />\n                        <span className=\"ml-2 text-gray-500\">\n                          Loading files...\n                        </span>\n                      </div>\n                    ) : (\n                      <div className=\"grid grid-cols-1 gap-2\">\n                        {filteredFiles.map((file, index) => {\n                          const isSelected = selectedFiles.some(\n                            (f) => f.path === file.path\n                          );\n                          return (\n                            <div\n                              key={index}\n                              onClick={() => toggleFileSelection(file)}\n                              className={`p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors ${\n                                isSelected\n                                  ? \"bg-blue-50 border-blue-300\"\n                                  : \"border-gray-200\"\n                              }`}\n                            >\n                              <div className=\"flex items-center gap-3\">\n                                <div className=\"flex-shrink-0\">\n                                  {isSelected && (\n                                    <div className=\"absolute -mt-1 -ml-1 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center\">\n                                      <Check className=\"w-3 h-3 text-white\" />\n                                    </div>\n                                  )}\n                                  {getFileIcon(file.name)}\n                                </div>\n                                <div className=\"flex-1 min-w-0\">\n                                  <div className=\"font-medium text-sm truncate\">\n                                    {file.name}\n                                  </div>\n                                  <div className=\"text-xs text-gray-500 flex gap-4\">\n                                    <span>{formatFileSize(file.size)}</span>\n                                    <span>{formatDate(file.modified)}</span>\n                                    {file.needs_processing && (\n                                      <span className=\"text-orange-600\">\n                                        Needs Processing\n                                      </span>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          );\n                        })}\n                        {filteredFiles.length === 0 && (\n                          <div className=\"text-center py-8 text-gray-500\">\n                            {searchQuery || fileFilter !== \"all\"\n                              ? \"No files match your search criteria\"\n                              : \"No files found in this folder\"}\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Footer */}\n                  <div className=\"p-4 border-t bg-gray-50 flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-600\">\n                      {selectedFiles.length} file(s) selected\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={onClose}\n                        className=\"px-4 py-2 text-gray-600 hover:bg-gray-200 rounded-md\"\n                      >\n                        Cancel\n                      </button>\n                      {showProcessOption && onProcessFiles && (\n                        <button\n                          onClick={handleProcessFiles}\n                          disabled={selectedFiles.length === 0}\n                          className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          Process Files ({selectedFiles.length})\n                        </button>\n                      )}\n                      <button\n                        onClick={handleSelectFiles}\n                        disabled={selectedFiles.length === 0}\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        Select Files ({selectedFiles.length})\n                      </button>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex-1 flex items-center justify-center text-gray-500\">\n                  <div className=\"text-center\">\n                    <Folder className=\"w-12 h-12 mx-auto mb-3 text-gray-400\" />\n                    <p>Select a folder to browse its files</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default FolderFileBrowser;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,SAAS,CACTC,CAAC,CACDC,MAAM,CACNC,KAAK,CACLC,SAAS,CACTC,GAAG,CACHC,QAAQ,KACH,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtB,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAMpB,IANqB,CACzBC,MAAM,CACNC,OAAO,CACPC,YAAY,CACZC,iBAAiB,CAAG,KAAK,CACzBC,cACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8B,cAAc,CAAEC,iBAAiB,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoC,SAAS,CAAEC,YAAY,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACsC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAEnDC,SAAS,CAAC,IAAM,CACd,GAAIsB,MAAM,CAAE,CACVmB,oBAAoB,CAAC,CAAC,CACxB,CACF,CAAC,CAAE,CAACnB,MAAM,CAAC,CAAC,CAEZ,KAAM,CAAAmB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACFL,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAAC0C,GAAG,CAAC,oCAAoC,CAAC,CACtEf,mBAAmB,CAACc,QAAQ,CAACE,IAAI,CAACC,iBAAiB,EAAI,EAAE,CAAC,CAC5D,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,OAAS,CACRV,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAY,eAAe,CAAG,KAAO,CAAAC,UAAU,EAAK,CAC5C,GAAI,CACFb,YAAY,CAAC,IAAI,CAAC,CAClBN,iBAAiB,CAACmB,UAAU,CAAC,CAE7B;AACA,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAzC,KAAK,CAACiD,IAAI,CAAC,oCAAoC,CAAE,CACtEC,WAAW,CAAEF,UACf,CAAC,CAAC,CAEFjB,cAAc,CAACU,QAAQ,CAACE,IAAI,CAACQ,KAAK,EAAI,EAAE,CAAC,CAC3C,CAAE,MAAON,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD;AACA,GAAI,KAAAO,qBAAA,CAAAC,sBAAA,CACF,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAtD,KAAK,CAAC0C,GAAG,CACnC,oCACF,CAAC,CACD,KAAM,CAAAa,WAAW,EAAAH,qBAAA,CAAGE,aAAa,CAACX,IAAI,CAACa,KAAK,UAAAJ,qBAAA,kBAAAC,sBAAA,CAAxBD,qBAAA,CAA0BK,OAAO,UAAAJ,sBAAA,iBAAjCA,sBAAA,CAAmCK,IAAI,CACxDC,CAAC,EAAKA,CAAC,CAACC,IAAI,GAAKZ,UACpB,CAAC,CACD,GAAIO,WAAW,CAAE,CACf;AACAxB,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAE,MAAO8B,aAAa,CAAE,CACtBf,OAAO,CAACD,KAAK,CAAC,kBAAkB,CAAEgB,aAAa,CAAC,CAChD9B,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAC,OAAS,CACRI,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA2B,WAAW,CAAIC,QAAQ,EAAK,KAAAC,mBAAA,CAChC,KAAM,CAAAC,SAAS,EAAAD,mBAAA,CAAGD,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,UAAAH,mBAAA,iBAAzBA,mBAAA,CAA2BI,WAAW,CAAC,CAAC,CAC1D,OAAQH,SAAS,EACf,IAAK,KAAK,CACR,mBAAOnD,IAAA,CAACT,QAAQ,EAACgE,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACtD,IAAK,MAAM,CACX,IAAK,KAAK,CACR,mBAAOvD,IAAA,CAACT,QAAQ,EAACgE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvD,IAAK,IAAI,CACT,IAAK,KAAK,CACR,mBAAOvD,IAAA,CAACT,QAAQ,EAACgE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvD,IAAK,KAAK,CACV,IAAK,MAAM,CACX,IAAK,KAAK,CACV,IAAK,KAAK,CACR,mBAAOvD,IAAA,CAACR,SAAS,EAAC+D,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACzD,QACE,mBAAOvD,IAAA,CAACV,IAAI,EAACiE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIC,KAAK,EAAK,CAChC,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAM,UAAU,CAAC,CAACP,KAAK,CAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,CAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGP,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,SAAS,EAAK,CAChC,MAAO,IAAI,CAAAC,IAAI,CAACD,SAAS,CAAG,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC,CACxD,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIC,IAAI,EAAK,CACpCrD,gBAAgB,CAAEsD,IAAI,EAAK,CACzB,KAAM,CAAAC,UAAU,CAAGD,IAAI,CAACE,IAAI,CAAE9B,CAAC,EAAKA,CAAC,CAACC,IAAI,GAAK0B,IAAI,CAAC1B,IAAI,CAAC,CACzD,GAAI4B,UAAU,CAAE,CACd,MAAO,CAAAD,IAAI,CAACG,MAAM,CAAE/B,CAAC,EAAKA,CAAC,CAACC,IAAI,GAAK0B,IAAI,CAAC1B,IAAI,CAAC,CACjD,CAAC,IAAM,CACL,MAAO,CAAC,GAAG2B,IAAI,CAAED,IAAI,CAAC,CACxB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAK,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAI3D,aAAa,CAAC4D,MAAM,CAAG,CAAC,CAAE,CAC5BrE,YAAY,CAACS,aAAa,CAAC,CAC3BV,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,KAAM,CAAAuE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI7D,aAAa,CAAC4D,MAAM,CAAG,CAAC,EAAInE,cAAc,CAAE,CAC9CA,cAAc,CAACO,aAAa,CAAC,CAC7BV,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,KAAM,CAAAwE,aAAa,CAAGhE,WAAW,CAAC4D,MAAM,CAAEJ,IAAI,EAAK,CACjD,KAAM,CAAAS,aAAa,CAAGT,IAAI,CAACU,IAAI,CAC5B5B,WAAW,CAAC,CAAC,CACb6B,QAAQ,CAAC7D,WAAW,CAACgC,WAAW,CAAC,CAAC,CAAC,CACtC,KAAM,CAAA8B,aAAa,CACjB5D,UAAU,GAAK,KAAK,EAAIgD,IAAI,CAACrB,SAAS,OAAAkC,MAAA,CAAS7D,UAAU,CAAE,CAC7D,MAAO,CAAAyD,aAAa,EAAIG,aAAa,CACvC,CAAC,CAAC,CAEF,GAAI,CAAC7E,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEP,IAAA,CAACZ,eAAe,EAAAkG,QAAA,cACdtF,IAAA,CAACb,MAAM,CAACoG,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrBlC,SAAS,CAAC,4EAA4E,CACtFqC,OAAO,CAAEpF,OAAQ,CAAA8E,QAAA,cAEjBpF,KAAA,CAACf,MAAM,CAACoG,GAAG,EACTC,OAAO,CAAE,CAAEK,KAAK,CAAE,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CACpCC,OAAO,CAAE,CAAEG,KAAK,CAAE,CAAC,CAAEJ,OAAO,CAAE,CAAE,CAAE,CAClCE,IAAI,CAAE,CAAEE,KAAK,CAAE,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CACjClC,SAAS,CAAC,6EAA6E,CACvFqC,OAAO,CAAGE,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAAT,QAAA,eAGpCpF,KAAA,QAAKqD,SAAS,CAAC,gDAAgD,CAAA+B,QAAA,eAC7DtF,IAAA,OAAIuD,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC9DtF,IAAA,WACE4F,OAAO,CAAEpF,OAAQ,CACjB+C,SAAS,CAAC,oCAAoC,CAAA+B,QAAA,cAE9CtF,IAAA,CAACP,CAAC,EAAC8D,SAAS,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,EACN,CAAC,cAENrD,KAAA,QAAKqD,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAE1CpF,KAAA,QAAKqD,SAAS,CAAC,+BAA+B,CAAA+B,QAAA,eAC5CtF,IAAA,OAAIuD,SAAS,CAAC,kBAAkB,CAAA+B,QAAA,CAAC,mBAAiB,CAAI,CAAC,CACtDlE,SAAS,EAAI,CAACN,cAAc,cAC3BZ,KAAA,QAAKqD,SAAS,CAAC,uCAAuC,CAAA+B,QAAA,eACpDtF,IAAA,CAACJ,SAAS,EAAC2D,SAAS,CAAC,sBAAsB,CAAE,CAAC,qBAEhD,EAAK,CAAC,cAENrD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAA+B,QAAA,EACvB1E,gBAAgB,CAACoF,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBAClChG,KAAA,WAEE0F,OAAO,CAAEA,CAAA,GAAM3D,eAAe,CAACgE,MAAM,CAAE,CACvC1C,SAAS,qEAAA8B,MAAA,CACPvE,cAAc,GAAKmF,MAAM,CACrB,oCAAoC,CACpC,UAAU,CACb,CAAAX,QAAA,eAEHpF,KAAA,QAAKqD,SAAS,CAAC,yBAAyB,CAAA+B,QAAA,eACtCtF,IAAA,CAACX,MAAM,EAACkE,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC5CvD,IAAA,SAAMuD,SAAS,CAAC,kBAAkB,CAAC4C,KAAK,CAAEF,MAAO,CAAAX,QAAA,CAC9CW,MAAM,CAAC7C,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,EAAI4C,MAAM,CAAC7C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAChD,CAAC,EACJ,CAAC,cACNrD,IAAA,QAAKuD,SAAS,CAAC,qCAAqC,CAAA+B,QAAA,CACjDW,MAAM,CACJ,CAAC,GAhBDC,KAiBC,CACT,CAAC,CACDtF,gBAAgB,CAACkE,MAAM,GAAK,CAAC,eAC5B9E,IAAA,QAAKuD,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,CAAC,uDAEvC,CAAK,CACN,EACE,CACN,EACE,CAAC,cAGNtF,IAAA,QAAKuD,SAAS,CAAC,sBAAsB,CAAA+B,QAAA,CAClCxE,cAAc,cACbZ,KAAA,CAAAE,SAAA,EAAAkF,QAAA,eAEEtF,IAAA,QAAKuD,SAAS,CAAC,yBAAyB,CAAA+B,QAAA,cACtCpF,KAAA,QAAKqD,SAAS,CAAC,yBAAyB,CAAA+B,QAAA,eACtCpF,KAAA,QAAKqD,SAAS,CAAC,iBAAiB,CAAA+B,QAAA,eAC9BtF,IAAA,CAACN,MAAM,EAAC6D,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC/FvD,IAAA,UACEoG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,iBAAiB,CAC7BC,KAAK,CAAEhF,WAAY,CACnBiF,QAAQ,CAAGT,CAAC,EAAKvE,cAAc,CAACuE,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE,CAChD/C,SAAS,CAAC,0CAA0C,CACrD,CAAC,EACC,CAAC,cACNrD,KAAA,WACEoG,KAAK,CAAE9E,UAAW,CAClB+E,QAAQ,CAAGT,CAAC,EAAKrE,aAAa,CAACqE,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE,CAC/C/C,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAEvCtF,IAAA,WAAQsG,KAAK,CAAC,KAAK,CAAAhB,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtCtF,IAAA,WAAQsG,KAAK,CAAC,KAAK,CAAAhB,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCtF,IAAA,WAAQsG,KAAK,CAAC,MAAM,CAAAhB,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClCtF,IAAA,WAAQsG,KAAK,CAAC,IAAI,CAAAhB,QAAA,CAAC,UAAQ,CAAQ,CAAC,cACpCtF,IAAA,WAAQsG,KAAK,CAAC,KAAK,CAAAhB,QAAA,CAAC,MAAI,CAAQ,CAAC,EAC3B,CAAC,EACN,CAAC,CACH,CAAC,cAGNtF,IAAA,QAAKuD,SAAS,CAAC,0BAA0B,CAAA+B,QAAA,CACtClE,SAAS,cACRlB,KAAA,QAAKqD,SAAS,CAAC,uCAAuC,CAAA+B,QAAA,eACpDtF,IAAA,CAACJ,SAAS,EAAC2D,SAAS,CAAC,oCAAoC,CAAE,CAAC,cAC5DvD,IAAA,SAAMuD,SAAS,CAAC,oBAAoB,CAAA+B,QAAA,CAAC,kBAErC,CAAM,CAAC,EACJ,CAAC,cAENpF,KAAA,QAAKqD,SAAS,CAAC,wBAAwB,CAAA+B,QAAA,EACpCN,aAAa,CAACgB,GAAG,CAAC,CAACxB,IAAI,CAAE0B,KAAK,GAAK,CAClC,KAAM,CAAAxB,UAAU,CAAGxD,aAAa,CAACyD,IAAI,CAClC9B,CAAC,EAAKA,CAAC,CAACC,IAAI,GAAK0B,IAAI,CAAC1B,IACzB,CAAC,CACD,mBACE9C,IAAA,QAEE4F,OAAO,CAAEA,CAAA,GAAMrB,mBAAmB,CAACC,IAAI,CAAE,CACzCjB,SAAS,4EAAA8B,MAAA,CACPX,UAAU,CACN,4BAA4B,CAC5B,iBAAiB,CACpB,CAAAY,QAAA,cAEHpF,KAAA,QAAKqD,SAAS,CAAC,yBAAyB,CAAA+B,QAAA,eACtCpF,KAAA,QAAKqD,SAAS,CAAC,eAAe,CAAA+B,QAAA,EAC3BZ,UAAU,eACT1E,IAAA,QAAKuD,SAAS,CAAC,wFAAwF,CAAA+B,QAAA,cACrGtF,IAAA,CAACL,KAAK,EAAC4D,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACrC,CACN,CACAP,WAAW,CAACwB,IAAI,CAACU,IAAI,CAAC,EACpB,CAAC,cACNhF,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAA+B,QAAA,eAC7BtF,IAAA,QAAKuD,SAAS,CAAC,8BAA8B,CAAA+B,QAAA,CAC1Cd,IAAI,CAACU,IAAI,CACP,CAAC,cACNhF,KAAA,QAAKqD,SAAS,CAAC,kCAAkC,CAAA+B,QAAA,eAC/CtF,IAAA,SAAAsF,QAAA,CAAO9B,cAAc,CAACgB,IAAI,CAACiC,IAAI,CAAC,CAAO,CAAC,cACxCzG,IAAA,SAAAsF,QAAA,CAAOnB,UAAU,CAACK,IAAI,CAACkC,QAAQ,CAAC,CAAO,CAAC,CACvClC,IAAI,CAACmC,gBAAgB,eACpB3G,IAAA,SAAMuD,SAAS,CAAC,iBAAiB,CAAA+B,QAAA,CAAC,kBAElC,CAAM,CACP,EACE,CAAC,EACH,CAAC,EACH,CAAC,EA/BDY,KAgCF,CAAC,CAEV,CAAC,CAAC,CACDlB,aAAa,CAACF,MAAM,GAAK,CAAC,eACzB9E,IAAA,QAAKuD,SAAS,CAAC,gCAAgC,CAAA+B,QAAA,CAC5ChE,WAAW,EAAIE,UAAU,GAAK,KAAK,CAChC,qCAAqC,CACrC,+BAA+B,CAChC,CACN,EACE,CACN,CACE,CAAC,cAGNtB,KAAA,QAAKqD,SAAS,CAAC,2DAA2D,CAAA+B,QAAA,eACxEpF,KAAA,QAAKqD,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,EACnCpE,aAAa,CAAC4D,MAAM,CAAC,mBACxB,EAAK,CAAC,cACN5E,KAAA,QAAKqD,SAAS,CAAC,YAAY,CAAA+B,QAAA,eACzBtF,IAAA,WACE4F,OAAO,CAAEpF,OAAQ,CACjB+C,SAAS,CAAC,sDAAsD,CAAA+B,QAAA,CACjE,QAED,CAAQ,CAAC,CACR5E,iBAAiB,EAAIC,cAAc,eAClCT,KAAA,WACE0F,OAAO,CAAEb,kBAAmB,CAC5B6B,QAAQ,CAAE1F,aAAa,CAAC4D,MAAM,GAAK,CAAE,CACrCvB,SAAS,CAAC,iHAAiH,CAAA+B,QAAA,EAC5H,iBACgB,CAACpE,aAAa,CAAC4D,MAAM,CAAC,GACvC,EAAQ,CACT,cACD5E,KAAA,WACE0F,OAAO,CAAEf,iBAAkB,CAC3B+B,QAAQ,CAAE1F,aAAa,CAAC4D,MAAM,GAAK,CAAE,CACrCvB,SAAS,CAAC,+GAA+G,CAAA+B,QAAA,EAC1H,gBACe,CAACpE,aAAa,CAAC4D,MAAM,CAAC,GACtC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACN,CAAC,cAEH9E,IAAA,QAAKuD,SAAS,CAAC,uDAAuD,CAAA+B,QAAA,cACpEpF,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAA+B,QAAA,eAC1BtF,IAAA,CAACX,MAAM,EAACkE,SAAS,CAAC,sCAAsC,CAAE,CAAC,cAC3DvD,IAAA,MAAAsF,QAAA,CAAG,qCAAmC,CAAG,CAAC,EACvC,CAAC,CACH,CACN,CACE,CAAC,EACH,CAAC,EACI,CAAC,CACH,CAAC,CACE,CAAC,CAEtB,CAAC,CAED,cAAe,CAAAjF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}