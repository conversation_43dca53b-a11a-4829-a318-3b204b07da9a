{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PanelBottom = createLucideIcon(\"PanelBottom\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"15\",\n  y2: \"15\",\n  key: \"o2sbyz\"\n}]]);\nexport { PanelBottom as default };", "map": {"version": 3, "names": ["PanelBottom", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\panel-bottom.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PanelBottom\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMyIgeDI9IjIxIiB5MT0iMTUiIHkyPSIxNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/panel-bottom\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelBottom = createLucideIcon('PanelBottom', [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', ry: '2', key: '1m3agn' }],\n  ['line', { x1: '3', x2: '21', y1: '15', y2: '15', key: 'o2sbyz' }],\n]);\n\nexport default PanelBottom;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}