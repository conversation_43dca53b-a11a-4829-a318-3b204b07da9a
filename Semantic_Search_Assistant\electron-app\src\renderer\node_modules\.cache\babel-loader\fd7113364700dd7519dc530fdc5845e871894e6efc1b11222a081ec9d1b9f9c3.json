{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst TextSelect = createLucideIcon(\"TextSelect\", [[\"path\", {\n  d: \"M5 3a2 2 0 0 0-2 2\",\n  key: \"y57alp\"\n}], [\"path\", {\n  d: \"M19 3a2 2 0 0 1 2 2\",\n  key: \"18rm91\"\n}], [\"path\", {\n  d: \"M21 19a2 2 0 0 1-2 2\",\n  key: \"1j7049\"\n}], [\"path\", {\n  d: \"M5 21a2 2 0 0 1-2-2\",\n  key: \"sbafld\"\n}], [\"path\", {\n  d: \"M9 3h1\",\n  key: \"1yesri\"\n}], [\"path\", {\n  d: \"M9 21h1\",\n  key: \"15o7lz\"\n}], [\"path\", {\n  d: \"M14 3h1\",\n  key: \"1ec4yj\"\n}], [\"path\", {\n  d: \"M14 21h1\",\n  key: \"v9vybs\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}], [\"path\", {\n  d: \"M21 9v1\",\n  key: \"mxsmne\"\n}], [\"path\", {\n  d: \"M3 14v1\",\n  key: \"vnatye\"\n}], [\"path\", {\n  d: \"M21 14v1\",\n  key: \"169vum\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"15\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"1758g8\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"17\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"197423\"\n}], [\"line\", {\n  x1: \"7\",\n  x2: \"13\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"37cgm6\"\n}]]);\nexport { TextSelect as default };", "map": {"version": 3, "names": ["TextSelect", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\text-select.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TextSelect\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAzYTIgMiAwIDAgMC0yIDIiIC8+CiAgPHBhdGggZD0iTTE5IDNhMiAyIDAgMCAxIDIgMiIgLz4KICA8cGF0aCBkPSJNMjEgMTlhMiAyIDAgMCAxLTIgMiIgLz4KICA8cGF0aCBkPSJNNSAyMWEyIDIgMCAwIDEtMi0yIiAvPgogIDxwYXRoIGQ9Ik05IDNoMSIgLz4KICA8cGF0aCBkPSJNOSAyMWgxIiAvPgogIDxwYXRoIGQ9Ik0xNCAzaDEiIC8+CiAgPHBhdGggZD0iTTE0IDIxaDEiIC8+CiAgPHBhdGggZD0iTTMgOXYxIiAvPgogIDxwYXRoIGQ9Ik0yMSA5djEiIC8+CiAgPHBhdGggZD0iTTMgMTR2MSIgLz4KICA8cGF0aCBkPSJNMjEgMTR2MSIgLz4KICA8bGluZSB4MT0iNyIgeDI9IjE1IiB5MT0iOCIgeTI9IjgiIC8+CiAgPGxpbmUgeDE9IjciIHgyPSIxNyIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjciIHgyPSIxMyIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/text-select\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TextSelect = createLucideIcon('TextSelect', [\n  ['path', { d: 'M5 3a2 2 0 0 0-2 2', key: 'y57alp' }],\n  ['path', { d: 'M19 3a2 2 0 0 1 2 2', key: '18rm91' }],\n  ['path', { d: 'M21 19a2 2 0 0 1-2 2', key: '1j7049' }],\n  ['path', { d: 'M5 21a2 2 0 0 1-2-2', key: 'sbafld' }],\n  ['path', { d: 'M9 3h1', key: '1yesri' }],\n  ['path', { d: 'M9 21h1', key: '15o7lz' }],\n  ['path', { d: 'M14 3h1', key: '1ec4yj' }],\n  ['path', { d: 'M14 21h1', key: 'v9vybs' }],\n  ['path', { d: 'M3 9v1', key: '1r0deq' }],\n  ['path', { d: 'M21 9v1', key: 'mxsmne' }],\n  ['path', { d: 'M3 14v1', key: 'vnatye' }],\n  ['path', { d: 'M21 14v1', key: '169vum' }],\n  ['line', { x1: '7', x2: '15', y1: '8', y2: '8', key: '1758g8' }],\n  ['line', { x1: '7', x2: '17', y1: '12', y2: '12', key: '197423' }],\n  ['line', { x1: '7', x2: '13', y1: '16', y2: '16', key: '37cgm6' }],\n]);\n\nexport default TextSelect;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}