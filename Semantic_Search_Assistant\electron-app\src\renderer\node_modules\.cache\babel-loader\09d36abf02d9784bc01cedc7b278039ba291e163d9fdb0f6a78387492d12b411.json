{"ast": null, "code": "import _objectSpread from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nconst animateMotionValue = function (valueName, value, target) {\n  let transition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, valueName) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const keyframes = getKeyframes(value, valueName, target, valueTransition);\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n    const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n    warning(isOriginAnimatable === isTargetAnimatable, \"You are trying to animate \".concat(valueName, \" from \\\"\").concat(originKeyframe, \"\\\" to \\\"\").concat(targetKeyframe, \"\\\". \").concat(originKeyframe, \" is not an animatable value - to enable this animation set \").concat(originKeyframe, \" to a value animatable to \").concat(targetKeyframe, \" via the `style` property.\"));\n    let options = _objectSpread(_objectSpread({\n      keyframes,\n      velocity: value.getVelocity(),\n      ease: \"easeOut\"\n    }, valueTransition), {}, {\n      delay: -elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      }\n    });\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = _objectSpread(_objectSpread({}, options), getDefaultTransition(valueName, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (!isOriginAnimatable || !isTargetAnimatable || instantAnimationState.current || valueTransition.type === false || MotionGlobalConfig.skipAnimations) {\n      /**\n       * If we can't animate this value, or the global instant animation flag is set,\n       * or this is simply defined as an instant transition, return an instant transition.\n       */\n      return createInstantAnimation(instantAnimationState.current ? _objectSpread(_objectSpread({}, options), {}, {\n        delay: 0\n      }) : options);\n    }\n    /**\n     * Animate via WAAPI if possible.\n     */\n    if (\n    /**\n     * If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    !transition.isHandoff && value.owner && value.owner.current instanceof HTMLElement &&\n    /**\n     * If we're outputting values to onUpdate then we can't use WAAPI as there's\n     * no way to read the value from WAAPI every frame.\n     */\n    !value.owner.getProps().onUpdate) {\n      const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n      if (acceleratedAnimation) return acceleratedAnimation;\n    }\n    /**\n     * If we didn't create an accelerated animation, create a JS animation\n     */\n    return animateValue(options);\n  };\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["warning", "secondsToMilliseconds", "instantAnimationState", "createAcceleratedAnimation", "createInstantAnimation", "getDefaultTransition", "isAnimatable", "getKeyframes", "getValueTransition", "isTransitionDefined", "animateValue", "MotionGlobalConfig", "animateMotionValue", "valueName", "value", "target", "transition", "arguments", "length", "undefined", "onComplete", "valueTransition", "delay", "elapsed", "keyframes", "originKeyframe", "targetKeyframe", "isOriginAnimatable", "isTargetAnimatable", "concat", "options", "_objectSpread", "velocity", "getVelocity", "ease", "onUpdate", "v", "set", "duration", "repeatDelay", "current", "type", "skipAnimations", "<PERSON><PERSON><PERSON><PERSON>", "owner", "HTMLElement", "getProps", "acceleratedAnimation"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\n\nconst animateMotionValue = (valueName, value, target, transition = {}) => {\n    return (onComplete) => {\n        const valueTransition = getValueTransition(transition, valueName) || {};\n        /**\n         * Most transition values are currently completely overwritten by value-specific\n         * transitions. In the future it'd be nicer to blend these transitions. But for now\n         * delay actually does inherit from the root transition if not value-specific.\n         */\n        const delay = valueTransition.delay || transition.delay || 0;\n        /**\n         * Elapsed isn't a public transition option but can be passed through from\n         * optimized appear effects in milliseconds.\n         */\n        let { elapsed = 0 } = transition;\n        elapsed = elapsed - secondsToMilliseconds(delay);\n        const keyframes = getKeyframes(value, valueName, target, valueTransition);\n        /**\n         * Check if we're able to animate between the start and end keyframes,\n         * and throw a warning if we're attempting to animate between one that's\n         * animatable and another that isn't.\n         */\n        const originKeyframe = keyframes[0];\n        const targetKeyframe = keyframes[keyframes.length - 1];\n        const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n        const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n        warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n        let options = {\n            keyframes,\n            velocity: value.getVelocity(),\n            ease: \"easeOut\",\n            ...valueTransition,\n            delay: -elapsed,\n            onUpdate: (v) => {\n                value.set(v);\n                valueTransition.onUpdate && valueTransition.onUpdate(v);\n            },\n            onComplete: () => {\n                onComplete();\n                valueTransition.onComplete && valueTransition.onComplete();\n            },\n        };\n        /**\n         * If there's no transition defined for this value, we can generate\n         * unqiue transition settings for this value.\n         */\n        if (!isTransitionDefined(valueTransition)) {\n            options = {\n                ...options,\n                ...getDefaultTransition(valueName, options),\n            };\n        }\n        /**\n         * Both WAAPI and our internal animation functions use durations\n         * as defined by milliseconds, while our external API defines them\n         * as seconds.\n         */\n        if (options.duration) {\n            options.duration = secondsToMilliseconds(options.duration);\n        }\n        if (options.repeatDelay) {\n            options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n        }\n        if (!isOriginAnimatable ||\n            !isTargetAnimatable ||\n            instantAnimationState.current ||\n            valueTransition.type === false ||\n            MotionGlobalConfig.skipAnimations) {\n            /**\n             * If we can't animate this value, or the global instant animation flag is set,\n             * or this is simply defined as an instant transition, return an instant transition.\n             */\n            return createInstantAnimation(instantAnimationState.current\n                ? { ...options, delay: 0 }\n                : options);\n        }\n        /**\n         * Animate via WAAPI if possible.\n         */\n        if (\n        /**\n         * If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        !transition.isHandoff &&\n            value.owner &&\n            value.owner.current instanceof HTMLElement &&\n            /**\n             * If we're outputting values to onUpdate then we can't use WAAPI as there's\n             * no way to read the value from WAAPI every frame.\n             */\n            !value.owner.getProps().onUpdate) {\n            const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n            if (acceleratedAnimation)\n                return acceleratedAnimation;\n        }\n        /**\n         * If we didn't create an accelerated animation, create a JS animation\n         */\n        return animateValue(options);\n    };\n};\n\nexport { animateMotionValue };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAClF,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AAEjE,MAAMC,kBAAkB,GAAG,SAAAA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAsB;EAAA,IAApBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,OAAQG,UAAU,IAAK;IACnB,MAAMC,eAAe,GAAGb,kBAAkB,CAACQ,UAAU,EAAEH,SAAS,CAAC,IAAI,CAAC,CAAC;IACvE;AACR;AACA;AACA;AACA;IACQ,MAAMS,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIN,UAAU,CAACM,KAAK,IAAI,CAAC;IAC5D;AACR;AACA;AACA;IACQ,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGP,UAAU;IAChCO,OAAO,GAAGA,OAAO,GAAGtB,qBAAqB,CAACqB,KAAK,CAAC;IAChD,MAAME,SAAS,GAAGjB,YAAY,CAACO,KAAK,EAAED,SAAS,EAAEE,MAAM,EAAEM,eAAe,CAAC;IACzE;AACR;AACA;AACA;AACA;IACQ,MAAMI,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,MAAME,cAAc,GAAGF,SAAS,CAACA,SAAS,CAACN,MAAM,GAAG,CAAC,CAAC;IACtD,MAAMS,kBAAkB,GAAGrB,YAAY,CAACO,SAAS,EAAEY,cAAc,CAAC;IAClE,MAAMG,kBAAkB,GAAGtB,YAAY,CAACO,SAAS,EAAEa,cAAc,CAAC;IAClE1B,OAAO,CAAC2B,kBAAkB,KAAKC,kBAAkB,+BAAAC,MAAA,CAA+BhB,SAAS,cAAAgB,MAAA,CAAUJ,cAAc,cAAAI,MAAA,CAASH,cAAc,UAAAG,MAAA,CAAMJ,cAAc,iEAAAI,MAAA,CAA8DJ,cAAc,gCAAAI,MAAA,CAA6BH,cAAc,+BAA8B,CAAC;IAClT,IAAII,OAAO,GAAAC,aAAA,CAAAA,aAAA;MACPP,SAAS;MACTQ,QAAQ,EAAElB,KAAK,CAACmB,WAAW,CAAC,CAAC;MAC7BC,IAAI,EAAE;IAAS,GACZb,eAAe;MAClBC,KAAK,EAAE,CAACC,OAAO;MACfY,QAAQ,EAAGC,CAAC,IAAK;QACbtB,KAAK,CAACuB,GAAG,CAACD,CAAC,CAAC;QACZf,eAAe,CAACc,QAAQ,IAAId,eAAe,CAACc,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDhB,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,CAAC,CAAC;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;MAC9D;IAAC,EACJ;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACX,mBAAmB,CAACY,eAAe,CAAC,EAAE;MACvCS,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACAD,OAAO,GACPzB,oBAAoB,CAACQ,SAAS,EAAEiB,OAAO,CAAC,CAC9C;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIA,OAAO,CAACQ,QAAQ,EAAE;MAClBR,OAAO,CAACQ,QAAQ,GAAGrC,qBAAqB,CAAC6B,OAAO,CAACQ,QAAQ,CAAC;IAC9D;IACA,IAAIR,OAAO,CAACS,WAAW,EAAE;MACrBT,OAAO,CAACS,WAAW,GAAGtC,qBAAqB,CAAC6B,OAAO,CAACS,WAAW,CAAC;IACpE;IACA,IAAI,CAACZ,kBAAkB,IACnB,CAACC,kBAAkB,IACnB1B,qBAAqB,CAACsC,OAAO,IAC7BnB,eAAe,CAACoB,IAAI,KAAK,KAAK,IAC9B9B,kBAAkB,CAAC+B,cAAc,EAAE;MACnC;AACZ;AACA;AACA;MACY,OAAOtC,sBAAsB,CAACF,qBAAqB,CAACsC,OAAO,GAAAT,aAAA,CAAAA,aAAA,KAChDD,OAAO;QAAER,KAAK,EAAE;MAAC,KACtBQ,OAAO,CAAC;IAClB;IACA;AACR;AACA;IACQ;IACA;AACR;AACA;AACA;AACA;IACQ,CAACd,UAAU,CAAC2B,SAAS,IACjB7B,KAAK,CAAC8B,KAAK,IACX9B,KAAK,CAAC8B,KAAK,CAACJ,OAAO,YAAYK,WAAW;IAC1C;AACZ;AACA;AACA;IACY,CAAC/B,KAAK,CAAC8B,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACX,QAAQ,EAAE;MAClC,MAAMY,oBAAoB,GAAG5C,0BAA0B,CAACW,KAAK,EAAED,SAAS,EAAEiB,OAAO,CAAC;MAClF,IAAIiB,oBAAoB,EACpB,OAAOA,oBAAoB;IACnC;IACA;AACR;AACA;IACQ,OAAOrC,YAAY,CAACoB,OAAO,CAAC;EAChC,CAAC;AACL,CAAC;AAED,SAASlB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}