{"ast": null, "code": "import _objectSpread from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"root\"];\n/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = entry => {\n  const callback = observerCallbacks.get(entry.target);\n  callback && callback(entry);\n};\nconst fireAllObserverCallbacks = entries => {\n  entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver(_ref) {\n  let {\n      root\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const lookupRoot = root || document;\n  /**\n   * If we don't have an observer lookup map for this root, create one.\n   */\n  if (!observers.has(lookupRoot)) {\n    observers.set(lookupRoot, {});\n  }\n  const rootObservers = observers.get(lookupRoot);\n  const key = JSON.stringify(options);\n  /**\n   * If we don't have an observer for this combination of root and settings,\n   * create one.\n   */\n  if (!rootObservers[key]) {\n    rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, _objectSpread({\n      root\n    }, options));\n  }\n  return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n  const rootInteresectionObserver = initIntersectionObserver(options);\n  observerCallbacks.set(element, callback);\n  rootInteresectionObserver.observe(element);\n  return () => {\n    observerCallbacks.delete(element);\n    rootInteresectionObserver.unobserve(element);\n  };\n}\nexport { observeIntersection };", "map": {"version": 3, "names": ["observerCallbacks", "WeakMap", "observers", "fireObserverCallback", "entry", "callback", "get", "target", "fireAllObserverCallbacks", "entries", "for<PERSON>ach", "initIntersectionObserver", "_ref", "root", "options", "_objectWithoutProperties", "_excluded", "lookupRoot", "document", "has", "set", "rootObservers", "key", "JSON", "stringify", "IntersectionObserver", "_objectSpread", "observeIntersection", "element", "rootInteresectionObserver", "observe", "delete", "unobserve"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs"], "sourcesContent": ["/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\nexport { observeIntersection };\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAID,OAAO,CAAC,CAAC;AAC/B,MAAME,oBAAoB,GAAIC,KAAK,IAAK;EACpC,MAAMC,QAAQ,GAAGL,iBAAiB,CAACM,GAAG,CAACF,KAAK,CAACG,MAAM,CAAC;EACpDF,QAAQ,IAAIA,QAAQ,CAACD,KAAK,CAAC;AAC/B,CAAC;AACD,MAAMI,wBAAwB,GAAIC,OAAO,IAAK;EAC1CA,OAAO,CAACC,OAAO,CAACP,oBAAoB,CAAC;AACzC,CAAC;AACD,SAASQ,wBAAwBA,CAAAC,IAAA,EAAuB;EAAA,IAAtB;MAAEC;IAAiB,CAAC,GAAAD,IAAA;IAATE,OAAO,GAAAC,wBAAA,CAAAH,IAAA,EAAAI,SAAA;EAChD,MAAMC,UAAU,GAAGJ,IAAI,IAAIK,QAAQ;EACnC;AACJ;AACA;EACI,IAAI,CAAChB,SAAS,CAACiB,GAAG,CAACF,UAAU,CAAC,EAAE;IAC5Bf,SAAS,CAACkB,GAAG,CAACH,UAAU,EAAE,CAAC,CAAC,CAAC;EACjC;EACA,MAAMI,aAAa,GAAGnB,SAAS,CAACI,GAAG,CAACW,UAAU,CAAC;EAC/C,MAAMK,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACV,OAAO,CAAC;EACnC;AACJ;AACA;AACA;EACI,IAAI,CAACO,aAAa,CAACC,GAAG,CAAC,EAAE;IACrBD,aAAa,CAACC,GAAG,CAAC,GAAG,IAAIG,oBAAoB,CAACjB,wBAAwB,EAAAkB,aAAA;MAAIb;IAAI,GAAKC,OAAO,CAAE,CAAC;EACjG;EACA,OAAOO,aAAa,CAACC,GAAG,CAAC;AAC7B;AACA,SAASK,mBAAmBA,CAACC,OAAO,EAAEd,OAAO,EAAET,QAAQ,EAAE;EACrD,MAAMwB,yBAAyB,GAAGlB,wBAAwB,CAACG,OAAO,CAAC;EACnEd,iBAAiB,CAACoB,GAAG,CAACQ,OAAO,EAAEvB,QAAQ,CAAC;EACxCwB,yBAAyB,CAACC,OAAO,CAACF,OAAO,CAAC;EAC1C,OAAO,MAAM;IACT5B,iBAAiB,CAAC+B,MAAM,CAACH,OAAO,CAAC;IACjCC,yBAAyB,CAACG,SAAS,CAACJ,OAAO,CAAC;EAChD,CAAC;AACL;AAEA,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}