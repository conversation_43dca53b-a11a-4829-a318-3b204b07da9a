{"ast": null, "code": "import _objectSpread from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"defaultTransition\"],\n  _excluded2 = [\"delay\", \"times\", \"type\"];\nimport { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence) {\n  let _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    {\n      defaultTransition = {}\n    } = _ref,\n    sequenceTransition = _objectWithoutProperties(_ref, _excluded);\n  let scope = arguments.length > 2 ? arguments[2] : undefined;\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = function (valueKeyframes, valueTransition, valueSequence) {\n      let elementIndex = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      let numElements = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n          delay = 0,\n          times = defaultOffset(valueKeyframesAsList),\n          type = \"keyframes\"\n        } = valueTransition,\n        remainingTransition = _objectWithoutProperties(valueTransition, _excluded2);\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numElements) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      if (numKeyframes <= 2 && type === \"spring\") {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = _objectSpread({}, remainingTransition);\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration !== null && duration !== void 0 ? duration : duration = defaultDuration;\n      const startTime = currentTime + calculatedDelay;\n      const targetTime = startTime + duration;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      /**\n       * Find all the elements specified in the definition and parse value\n       * keyframes from their timeline definitions.\n       */\n      const elements = resolveElements(subject, scope, elementCache);\n      const numElements = elements.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const element = elements[elementIndex];\n        const subjectSequence = getSubjectSequence(element, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = _objectSpread(_objectSpread({}, defaultTransition), {}, {\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset\n      }, sequenceTransition);\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] ? _objectSpread(_objectSpread({}, transition), transition[key]) : _objectSpread({}, transition);\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["createGeneratorEasing", "resolveElements", "defaultOffset", "fillOffset", "progress", "secondsToMilliseconds", "isMotionValue", "calcNextTime", "addKeyframes", "compareByTime", "defaultSegmentEasing", "createAnimationsFromSequence", "sequence", "_ref", "arguments", "length", "undefined", "defaultTransition", "sequenceTransition", "_objectWithoutProperties", "_excluded", "scope", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numElements", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "remainingTransition", "_excluded2", "ease", "calculatedDelay", "numKeyframes", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "_objectSpread", "springEasing", "startTime", "targetTime", "remainder", "unshift", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "elements", "element", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "sort", "valueOffset", "valueEasing", "value", "easing", "push", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numElements = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numElements)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            if (numKeyframes <= 2 && type === \"spring\") {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration !== null && duration !== void 0 ? duration : (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            const targetTime = startTime + duration;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            /**\n             * Find all the elements specified in the definition and parse value\n             * keyframes from their timeline definitions.\n             */\n            const elements = resolveElements(subject, scope, elementCache);\n            const numElements = elements.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const element = elements[elementIndex];\n                const subjectSequence = getSubjectSequence(element, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition[key]\n        ? { ...transition, ...transition[key] }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": ";;;;AAAA,SAASA,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,SAASC,4BAA4BA,CAACC,QAAQ,EAAiE;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAX,CAAC,CAAC;IAAtD;MAAEG,iBAAiB,GAAG,CAAC;IAAyB,CAAC,GAAAJ,IAAA;IAApBK,kBAAkB,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAAA,IAASC,KAAK,GAAAP,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACzG,MAAMM,eAAe,GAAGL,iBAAiB,CAACM,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,CAAC,CAAC;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,QAAQ,CAACG,MAAM,EAAEiB,CAAC,EAAE,EAAE;IACtC,MAAMC,OAAO,GAAGrB,QAAQ,CAACoB,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC7BL,UAAU,CAACM,GAAG,CAACD,OAAO,EAAEH,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BL,UAAU,CAACM,GAAG,CAACD,OAAO,CAACI,IAAI,EAAE9B,YAAY,CAACuB,WAAW,EAAEG,OAAO,CAACK,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACW,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKtB,SAAS,EAAE;MAC7Bc,WAAW,GAAGvB,YAAY,CAACuB,WAAW,EAAEW,UAAU,CAACH,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIc,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAG,SAAAA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAwC;MAAA,IAAtCC,YAAY,GAAAjC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAEkC,WAAW,GAAAlC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAC3G,MAAMmC,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;UAAEO,KAAK,GAAG,CAAC;UAAEC,KAAK,GAAGlD,aAAa,CAAC+C,oBAAoB,CAAC;UAAEI,IAAI,GAAG;QAAoC,CAAC,GAAGR,eAAe;QAAvCS,mBAAmB,GAAAnC,wBAAA,CAAK0B,eAAe,EAAAU,UAAA;MAC9H,IAAI;QAAEC,IAAI,GAAGvC,iBAAiB,CAACuC,IAAI,IAAI,SAAS;QAAEjC;MAAS,CAAC,GAAGsB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMY,eAAe,GAAG,OAAON,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMO,YAAY,GAAGT,oBAAoB,CAAClC,MAAM;MAChD,IAAI2C,YAAY,IAAI,CAAC,IAAIL,IAAI,KAAK,QAAQ,EAAE;QACxC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIM,aAAa,GAAG,GAAG;QACvB,IAAID,YAAY,KAAK,CAAC,IAClBE,sBAAsB,CAACX,oBAAoB,CAAC,EAAE;UAC9C,MAAMY,KAAK,GAAGZ,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/DU,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAAC,aAAA,KAAQX,mBAAmB,CAAE;QACnD,IAAI/B,QAAQ,KAAKP,SAAS,EAAE;UACxBgD,gBAAgB,CAACzC,QAAQ,GAAGlB,qBAAqB,CAACkB,QAAQ,CAAC;QAC/D;QACA,MAAM2C,YAAY,GAAGlE,qBAAqB,CAACgE,gBAAgB,EAAEL,aAAa,CAAC;QAC3EH,IAAI,GAAGU,YAAY,CAACV,IAAI;QACxBjC,QAAQ,GAAG2C,YAAY,CAAC3C,QAAQ;MACpC;MACAA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAIA,QAAQ,GAAGD,eAAgB;MAClF,MAAM6C,SAAS,GAAGrC,WAAW,GAAG2B,eAAe;MAC/C,MAAMW,UAAU,GAAGD,SAAS,GAAG5C,QAAQ;MACvC;AACZ;AACA;MACY,IAAI6B,KAAK,CAACrC,MAAM,KAAK,CAAC,IAAIqC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMiB,SAAS,GAAGjB,KAAK,CAACrC,MAAM,GAAGkC,oBAAoB,CAAClC,MAAM;MAC5DsD,SAAS,GAAG,CAAC,IAAIlE,UAAU,CAACiD,KAAK,EAAEiB,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYpB,oBAAoB,CAAClC,MAAM,KAAK,CAAC,IAC7BkC,oBAAoB,CAACqB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACY9D,YAAY,CAACsC,aAAa,EAAEG,oBAAoB,EAAEO,IAAI,EAAEJ,KAAK,EAAEe,SAAS,EAAEC,UAAU,CAAC;MACrF1B,WAAW,GAAGoB,IAAI,CAACS,GAAG,CAACd,eAAe,GAAGlC,QAAQ,EAAEmB,WAAW,CAAC;MAC/DX,aAAa,GAAG+B,IAAI,CAACS,GAAG,CAACH,UAAU,EAAErC,aAAa,CAAC;IACvD,CAAC;IACD,IAAIzB,aAAa,CAACiC,OAAO,CAAC,EAAE;MACxB,MAAMiC,eAAe,GAAGC,kBAAkB,CAAClC,OAAO,EAAEb,SAAS,CAAC;MAC9DiB,oBAAoB,CAACH,SAAS,EAAEC,UAAU,EAAEiC,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAMG,QAAQ,GAAG1E,eAAe,CAACsC,OAAO,EAAElB,KAAK,EAAEM,YAAY,CAAC;MAC9D,MAAMqB,WAAW,GAAG2B,QAAQ,CAAC5D,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAIgC,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGC,WAAW,EAAED,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgBP,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAMmC,OAAO,GAAGD,QAAQ,CAAC5B,YAAY,CAAC;QACtC,MAAMyB,eAAe,GAAGC,kBAAkB,CAACG,OAAO,EAAElD,SAAS,CAAC;QAC9D,KAAK,MAAMmD,GAAG,IAAIrC,SAAS,EAAE;UACzBG,oBAAoB,CAACH,SAAS,CAACqC,GAAG,CAAC,EAAEC,kBAAkB,CAACrC,UAAU,EAAEoC,GAAG,CAAC,EAAEH,gBAAgB,CAACG,GAAG,EAAEL,eAAe,CAAC,EAAEzB,YAAY,EAAEC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACAnB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIY,WAAW;EAC9B;EACA;AACJ;AACA;EACIhB,SAAS,CAACqD,OAAO,CAAC,CAACC,cAAc,EAAEJ,OAAO,KAAK;IAC3C,KAAK,MAAMC,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAMlC,aAAa,GAAGkC,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACY/B,aAAa,CAACmC,IAAI,CAACxE,aAAa,CAAC;MACjC,MAAM+B,SAAS,GAAG,EAAE;MACpB,MAAM0C,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,aAAa,CAAC/B,MAAM,EAAEiB,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEM,EAAE;UAAE8C,KAAK;UAAEC;QAAO,CAAC,GAAGvC,aAAa,CAACd,CAAC,CAAC;QAC9CQ,SAAS,CAAC8C,IAAI,CAACF,KAAK,CAAC;QACrBF,WAAW,CAACI,IAAI,CAAClF,QAAQ,CAAC,CAAC,EAAE2B,aAAa,EAAEO,EAAE,CAAC,CAAC;QAChD6C,WAAW,CAACG,IAAI,CAACD,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACZ,OAAO,CAAC,CAAC,CAAC;QACtB9B,SAAS,CAAC8B,OAAO,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/B2C,WAAW,CAACb,OAAO,CAAC5D,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIwE,WAAW,CAACA,WAAW,CAACnE,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3CmE,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC;QACnB9C,SAAS,CAAC8C,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAAC9D,oBAAoB,CAAC+D,GAAG,CAACX,OAAO,CAAC,EAAE;QACpCpD,oBAAoB,CAACU,GAAG,CAAC0C,OAAO,EAAE;UAC9BpC,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAM+C,UAAU,GAAGhE,oBAAoB,CAACiE,GAAG,CAACb,OAAO,CAAC;MACpDY,UAAU,CAAChD,SAAS,CAACqC,GAAG,CAAC,GAAGrC,SAAS;MACrCgD,UAAU,CAAC/C,UAAU,CAACoC,GAAG,CAAC,GAAAZ,aAAA,CAAAA,aAAA,KACnBhD,iBAAiB;QACpBM,QAAQ,EAAEQ,aAAa;QACvByB,IAAI,EAAE2B,WAAW;QACjB/B,KAAK,EAAE8B;MAAW,GACfhE,kBAAkB,CACxB;IACL;EACJ,CAAC,CAAC;EACF,OAAOM,oBAAoB;AAC/B;AACA,SAASiD,kBAAkBA,CAAClC,OAAO,EAAEb,SAAS,EAAE;EAC5C,CAACA,SAAS,CAAC6D,GAAG,CAAChD,OAAO,CAAC,IAAIb,SAAS,CAACQ,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOb,SAAS,CAAC+D,GAAG,CAAClD,OAAO,CAAC;AACjC;AACA,SAASmC,gBAAgBA,CAACrC,IAAI,EAAEX,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACW,IAAI,CAAC,EAChBX,SAAS,CAACW,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOX,SAAS,CAACW,IAAI,CAAC;AAC1B;AACA,SAASa,eAAeA,CAACV,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAASsC,kBAAkBA,CAACrC,UAAU,EAAEoC,GAAG,EAAE;EACzC,OAAOpC,UAAU,CAACoC,GAAG,CAAC,GAAAZ,aAAA,CAAAA,aAAA,KACXxB,UAAU,GAAKA,UAAU,CAACoC,GAAG,CAAC,IAAAZ,aAAA,KAC9BxB,UAAU,CAAE;AAC3B;AACA,MAAMiD,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAM/B,sBAAsB,GAAIpB,SAAS,IAAKA,SAAS,CAACoD,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAAS/E,4BAA4B,EAAEmE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}