{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Signpost = createLucideIcon(\"Signpost\", [[\"path\", {\n  d: \"M12 3v3\",\n  key: \"1n5kay\"\n}], [\"path\", {\n  d: \"M18.5 13h-13L2 9.5 5.5 6h13L22 9.5Z\",\n  key: \"27os56\"\n}], [\"path\", {\n  d: \"M12 13v8\",\n  key: \"1l5pq0\"\n}]]);\nexport { Signpost as default };", "map": {"version": 3, "names": ["Signpost", "createLucideIcon", "d", "key"], "sources": ["E:\\PROJECT\\Semantic_Search_Assistant\\electron-app\\src\\renderer\\node_modules\\lucide-react\\src\\icons\\signpost.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Signpost\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YzIiAvPgogIDxwYXRoIGQ9Ik0xOC41IDEzaC0xM0wyIDkuNSA1LjUgNmgxM0wyMiA5LjVaIiAvPgogIDxwYXRoIGQ9Ik0xMiAxM3Y4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/signpost\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Signpost = createLucideIcon('Signpost', [\n  ['path', { d: 'M12 3v3', key: '1n5kay' }],\n  ['path', { d: 'M18.5 13h-13L2 9.5 5.5 6h13L22 9.5Z', key: '27os56' }],\n  ['path', { d: 'M12 13v8', key: '1l5pq0' }],\n]);\n\nexport default Signpost;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}