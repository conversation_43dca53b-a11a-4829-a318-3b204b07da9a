{"ast": null, "code": "import _objectSpread from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transition\", \"transitionEnd\"];\nimport { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation(_ref, key) {\n  let {\n    protectedKeys,\n    needsAnimating\n  } = _ref;\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n  const current = value.get();\n  if (Array.isArray(target)) {\n    for (let i = 0; i < target.length; i++) {\n      if (target[i] !== current) return true;\n    }\n  } else {\n    return current !== target;\n  }\n}\nfunction animateTarget(visualElement, definition) {\n  let {\n    delay = 0,\n    transitionOverride,\n    type\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let _visualElement$makeTa = visualElement.makeTargetAnimatable(definition),\n    {\n      transition = visualElement.getDefaultTransition(),\n      transitionEnd\n    } = _visualElement$makeTa,\n    target = _objectWithoutProperties(_visualElement$makeTa, _excluded);\n  const willChange = visualElement.getValue(\"willChange\");\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key);\n    const valueTarget = target[key];\n    if (!value || valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = _objectSpread({\n      delay,\n      elapsed: 0\n    }, getValueTransition(transition || {}, key));\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    if (window.HandoffAppearAnimations) {\n      const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n      if (appearId) {\n        const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n        if (elapsed !== null) {\n          valueTransition.elapsed = elapsed;\n          valueTransition.isHandoff = true;\n        }\n      }\n    }\n    let canSkip = !valueTransition.isHandoff && !hasKeyframesChanged(value, valueTarget);\n    if (valueTransition.type === \"spring\" && (value.getVelocity() || valueTransition.velocity)) {\n      canSkip = false;\n    }\n    /**\n     * Temporarily disable skipping animations if there's an animation in\n     * progress. Better would be to track the current target of a value\n     * and compare that against valueTarget.\n     */\n    if (value.animation) {\n      canSkip = false;\n    }\n    if (canSkip) continue;\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key) ? {\n      type: false\n    } : valueTransition));\n    const animation = value.animation;\n    if (isWillChangeMotionValue(willChange)) {\n      willChange.add(key);\n      animation.then(() => willChange.remove(key));\n    }\n    animations.push(animation);\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      transitionEnd && setTarget(visualElement, transitionEnd);\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["transformProps", "optimizedAppearDataAttribute", "animateMotionValue", "isWillChangeMotionValue", "<PERSON><PERSON><PERSON><PERSON>", "getValueTransition", "frame", "shouldBlockAnimation", "_ref", "key", "protected<PERSON><PERSON>s", "needsAnimating", "shouldBlock", "hasOwnProperty", "hasKeyframesChanged", "value", "target", "current", "get", "Array", "isArray", "i", "length", "animate<PERSON>arget", "visualElement", "definition", "delay", "transitionOverride", "type", "arguments", "undefined", "_visualElement$makeTa", "makeTargetAnimatable", "transition", "getDefaultTransition", "transitionEnd", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "animations", "animationTypeState", "animationState", "getState", "valueTarget", "valueTransition", "_objectSpread", "elapsed", "window", "HandoffAppearAnimations", "appearId", "getProps", "<PERSON><PERSON><PERSON><PERSON>", "canSkip", "getVelocity", "velocity", "animation", "start", "shouldReduceMotion", "has", "add", "then", "remove", "push", "Promise", "all"], "sources": ["E:/PROJECT/Semantic_Search_Assistant/electron-app/src/renderer/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n    const current = value.get();\n    if (Array.isArray(target)) {\n        for (let i = 0; i < target.length; i++) {\n            if (target[i] !== current)\n                return true;\n        }\n    }\n    else {\n        return current !== target;\n    }\n}\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            elapsed: 0,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (window.HandoffAppearAnimations) {\n            const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n            if (appearId) {\n                const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n                if (elapsed !== null) {\n                    valueTransition.elapsed = elapsed;\n                    valueTransition.isHandoff = true;\n                }\n            }\n        }\n        let canSkip = !valueTransition.isHandoff &&\n            !hasKeyframesChanged(value, valueTarget);\n        if (valueTransition.type === \"spring\" &&\n            (value.getVelocity() || valueTransition.velocity)) {\n            canSkip = false;\n        }\n        /**\n         * Temporarily disable skipping animations if there's an animation in\n         * progress. Better would be to track the current target of a value\n         * and compare that against valueTarget.\n         */\n        if (value.animation) {\n            canSkip = false;\n        }\n        if (canSkip)\n            continue;\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key)\n            ? { type: false }\n            : valueTransition));\n        const animation = value.animation;\n        if (isWillChangeMotionValue(willChange)) {\n            willChange.add(key);\n            animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            transitionEnd && setTarget(visualElement, transitionEnd);\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAAC,IAAA,EAAoCC,GAAG,EAAE;EAAA,IAAxC;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAAH,IAAA;EAC3D,MAAMI,WAAW,GAAGF,aAAa,CAACG,cAAc,CAACJ,GAAG,CAAC,IAAIE,cAAc,CAACF,GAAG,CAAC,KAAK,IAAI;EACrFE,cAAc,CAACF,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOG,WAAW;AACtB;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACxC,MAAMC,OAAO,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;EAC3B,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAIL,MAAM,CAACK,CAAC,CAAC,KAAKJ,OAAO,EACrB,OAAO,IAAI;IACnB;EACJ,CAAC,MACI;IACD,OAAOA,OAAO,KAAKD,MAAM;EAC7B;AACJ;AACA,SAASO,aAAaA,CAACC,aAAa,EAAEC,UAAU,EAAgD;EAAA,IAA9C;IAAEC,KAAK,GAAG,CAAC;IAAEC,kBAAkB;IAAEC;EAAK,CAAC,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC1F,IAAAE,qBAAA,GAAsFP,aAAa,CAACQ,oBAAoB,CAACP,UAAU,CAAC;IAAhI;MAAEQ,UAAU,GAAGT,aAAa,CAACU,oBAAoB,CAAC,CAAC;MAAEC;IAAyB,CAAC,GAAAJ,qBAAA;IAARf,MAAM,GAAAoB,wBAAA,CAAAL,qBAAA,EAAAM,SAAA;EACjF,MAAMC,UAAU,GAAGd,aAAa,CAACe,QAAQ,CAAC,YAAY,CAAC;EACvD,IAAIZ,kBAAkB,EAClBM,UAAU,GAAGN,kBAAkB;EACnC,MAAMa,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGb,IAAI,IAC3BJ,aAAa,CAACkB,cAAc,IAC5BlB,aAAa,CAACkB,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACf,IAAI,CAAC;EACjD,KAAK,MAAMnB,GAAG,IAAIO,MAAM,EAAE;IACtB,MAAMD,KAAK,GAAGS,aAAa,CAACe,QAAQ,CAAC9B,GAAG,CAAC;IACzC,MAAMmC,WAAW,GAAG5B,MAAM,CAACP,GAAG,CAAC;IAC/B,IAAI,CAACM,KAAK,IACN6B,WAAW,KAAKd,SAAS,IACxBW,kBAAkB,IACflC,oBAAoB,CAACkC,kBAAkB,EAAEhC,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAMoC,eAAe,GAAAC,aAAA;MACjBpB,KAAK;MACLqB,OAAO,EAAE;IAAC,GACP1C,kBAAkB,CAAC4B,UAAU,IAAI,CAAC,CAAC,EAAExB,GAAG,CAAC,CAC/C;IACD;AACR;AACA;AACA;IACQ,IAAIuC,MAAM,CAACC,uBAAuB,EAAE;MAChC,MAAMC,QAAQ,GAAG1B,aAAa,CAAC2B,QAAQ,CAAC,CAAC,CAAClD,4BAA4B,CAAC;MACvE,IAAIiD,QAAQ,EAAE;QACV,MAAMH,OAAO,GAAGC,MAAM,CAACC,uBAAuB,CAACC,QAAQ,EAAEzC,GAAG,EAAEM,KAAK,EAAET,KAAK,CAAC;QAC3E,IAAIyC,OAAO,KAAK,IAAI,EAAE;UAClBF,eAAe,CAACE,OAAO,GAAGA,OAAO;UACjCF,eAAe,CAACO,SAAS,GAAG,IAAI;QACpC;MACJ;IACJ;IACA,IAAIC,OAAO,GAAG,CAACR,eAAe,CAACO,SAAS,IACpC,CAACtC,mBAAmB,CAACC,KAAK,EAAE6B,WAAW,CAAC;IAC5C,IAAIC,eAAe,CAACjB,IAAI,KAAK,QAAQ,KAChCb,KAAK,CAACuC,WAAW,CAAC,CAAC,IAAIT,eAAe,CAACU,QAAQ,CAAC,EAAE;MACnDF,OAAO,GAAG,KAAK;IACnB;IACA;AACR;AACA;AACA;AACA;IACQ,IAAItC,KAAK,CAACyC,SAAS,EAAE;MACjBH,OAAO,GAAG,KAAK;IACnB;IACA,IAAIA,OAAO,EACP;IACJtC,KAAK,CAAC0C,KAAK,CAACvD,kBAAkB,CAACO,GAAG,EAAEM,KAAK,EAAE6B,WAAW,EAAEpB,aAAa,CAACkC,kBAAkB,IAAI1D,cAAc,CAAC2D,GAAG,CAAClD,GAAG,CAAC,GAC7G;MAAEmB,IAAI,EAAE;IAAM,CAAC,GACfiB,eAAe,CAAC,CAAC;IACvB,MAAMW,SAAS,GAAGzC,KAAK,CAACyC,SAAS;IACjC,IAAIrD,uBAAuB,CAACmC,UAAU,CAAC,EAAE;MACrCA,UAAU,CAACsB,GAAG,CAACnD,GAAG,CAAC;MACnB+C,SAAS,CAACK,IAAI,CAAC,MAAMvB,UAAU,CAACwB,MAAM,CAACrD,GAAG,CAAC,CAAC;IAChD;IACA+B,UAAU,CAACuB,IAAI,CAACP,SAAS,CAAC;EAC9B;EACA,IAAIrB,aAAa,EAAE;IACf6B,OAAO,CAACC,GAAG,CAACzB,UAAU,CAAC,CAACqB,IAAI,CAAC,MAAM;MAC/B1B,aAAa,IAAI/B,SAAS,CAACoB,aAAa,EAAEW,aAAa,CAAC;IAC5D,CAAC,CAAC;EACN;EACA,OAAOK,UAAU;AACrB;AAEA,SAASjB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}